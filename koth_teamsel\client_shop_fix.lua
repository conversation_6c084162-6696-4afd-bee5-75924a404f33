-- SHOP UI FIX: Ensure shop menus display properly when interacting with peds

-- Override vehicle menu event to ensure proper display
RegisterNetEvent('koth:openVehicleMenu', function(items)
    print('[KOTH SHOP FIX] Opening vehicle menu with proper UI handling')
    
    -- Ensure overlay is visible first
    SendNUIMessage({ 
        action = 'showOverlay'
    })
    
    -- Small delay to ensure overlay is ready
    Citizen.SetTimeout(100, function()
        -- Use provided items or comprehensive vehicle data
        local vehicles = items or {
            { name = 'Blista', cost = 8000, rent = 1200, img = 'images/vehicles/blista.png' },
            { name = 'Futo', cost = 12000, rent = 1800, img = 'images/vehicles/futo.png' },
            { name = 'Sultan', cost = 15000, rent = 2200, img = 'images/vehicles/sultan.png' },
            { name = 'Elegy', cost = 18000, rent = 2700, img = 'images/vehicles/elegy.png' },
            { name = 'Kuruma', cost = 22000, rent = 3300, img = 'images/vehicles/kuruma.png' },
            { name = 'Armored Kuruma', cost = 35000, rent = 5200, img = 'images/vehicles/kuruma_armored.png' },
            { name = 'Insurgent', cost = 45000, rent = 6700, img = 'images/vehicles/insurgent.png' },
            { name = 'Technical', cost = 28000, rent = 4200, img = 'images/vehicles/technical.png' },
            { name = 'Sandking XL', cost = 32000, rent = 4800, img = 'images/vehicles/sandking.png' },
            { name = 'Mesa', cost = 25000, rent = 3700, img = 'images/vehicles/mesa.png' },
            { name = 'Buzzard', cost = 85000, rent = 12700, img = 'images/vehicles/buzzard.png' },
            { name = 'Savage', cost = 120000, rent = 18000, img = 'images/vehicles/savage.png' },
            { name = 'Rhino Tank', cost = 150000, rent = 22500, img = 'images/vehicles/rhino.png' },
            { name = 'Hydra', cost = 200000, rent = 30000, img = 'images/vehicles/hydra.png' }
        }
        
        -- Request fresh data from server
        TriggerServerEvent('koth:getMoneyForVehicleShop', vehicles)
    end)
end)

-- Override class menu event to ensure proper display
RegisterNetEvent('koth:openClassMenu', function(items)
    print('[KOTH SHOP FIX] Opening class menu with proper UI handling')
    
    -- Ensure overlay is visible first
    SendNUIMessage({ 
        action = 'showOverlay'
    })
    
    -- Small delay to ensure overlay is ready
    Citizen.SetTimeout(100, function()
        -- Use provided items or exact classes
        local classes = items or {
            {
                id = 'assault',
                name = 'Assault',
                unlock = 'Unlocked',
                img = 'images/classes/assault.png',
                requiredLevel = 1
            },
            {
                id = 'medic',
                name = 'Medic',
                unlock = 'Unlock at level 5',
                img = 'images/classes/medic.png',
                requiredLevel = 5
            },
            {
                id = 'engineer',
                name = 'Engineer',
                unlock = 'Unlock at level 15',
                img = 'images/classes/engineer.png',
                requiredLevel = 15
            },
            {
                id = 'heavy',
                name = 'Heavy',
                unlock = 'Unlock at level 25',
                img = 'images/classes/heavy.png',
                requiredLevel = 25
            },
            {
                id = 'scout',
                name = 'Scout',
                unlock = 'Unlock at level 40',
                img = 'images/classes/scout.png',
                requiredLevel = 40
            }
        }
        
        -- Request fresh data from server
        TriggerServerEvent('koth:getDataForClassShop', classes)
    end)
end)

-- Fix for vehicle shop display
RegisterNetEvent('koth:showVehicleShopWithMoney', function(data)
    print('[KOTH SHOP FIX] Showing vehicle shop with money:', data.money)
    
    -- Ensure overlay is visible
    SendNUIMessage({ 
        action = 'showOverlay'
    })
    
    -- Show the vehicle shop specifically
    Citizen.SetTimeout(50, function()
        SendNUIMessage({
            action = 'showVehicleShop',
            items = data.vehicles,
            money = data.money
        })
        SetNuiFocus(true, true)
    end)
end)

-- Fix for class shop display
RegisterNetEvent('koth:showClassShopWithData', function(data)
    print('[KOTH SHOP FIX] Showing class shop with money:', data.money, 'level:', data.level)
    
    -- Add locked status based on player level
    for i, class in ipairs(data.classes) do
        class.locked = data.level < class.requiredLevel
    end
    
    -- Ensure overlay is visible
    SendNUIMessage({ 
        action = 'showOverlay'
    })
    
    -- Show the class selection specifically
    Citizen.SetTimeout(50, function()
        SendNUIMessage({
            action = 'showClassSelection',
            items = data.classes,
            playerLevel = data.level,
            money = data.money
        })
        SetNuiFocus(true, true)
    end)
end)

-- Fix for weapon shop display
RegisterNetEvent('koth:showWeaponShopWithMoney', function(data)
    print('[KOTH SHOP FIX] Showing weapon shop with money:', data.money)
    
    -- Ensure overlay is visible
    SendNUIMessage({ 
        action = 'showOverlay'
    })
    
    -- Show the weapon shop specifically
    Citizen.SetTimeout(50, function()
        SendNUIMessage({
            action = 'showWeaponShop',
            class = data.class,
            weapons = data.weapons,
            money = data.money
        })
        SetNuiFocus(true, true)
    end)
end)

print('[KOTH SHOP FIX] Shop display fix loaded')
