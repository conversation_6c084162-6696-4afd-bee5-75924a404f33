# KOTH Team Selection System - Fixed Version

## What Was Fixed

### 1. **Team Persistence**
- Added `current_team` column to database (see migration below)
- Players now stay on their team after disconnect/reconnect
- Team is automatically restored when player rejoins

### 2. **Spawn System**
- Players automatically spawn at their team base when they have a team
- No need to reselect team on reconnect
- Proper spawn coordinates with ground detection

### 3. **HUD System**
- HUD initializes immediately on spawn
- Shows player money, XP, level, and team counts
- Updates in real-time when values change

### 4. **Shop Systems**
- Vehicle and weapon shops now properly show player money
- Prices are checked against database values
- Purchase buttons are disabled if player can't afford

### 5. **KOTH Zone**
- Zone capture mechanics work properly
- Points awarded to controlling team
- Visual indicators on map and HUD

### 6. **PVP System**
- PVP is enabled for all players
- Kill rewards (money + XP) work properly
- Zone kills give double rewards

## Database Migration

If you already have the `koth_players` table without the team column, run this SQL:

```sql
ALTER TABLE `koth_players` ADD COLUMN `current_team` varchar(10) DEFAULT NULL AFTER `player_name`;
```

## Installation Instructions

1. **Stop your server**

2. **Update Database**
   - Import the updated `database.sql` file
   - Or run the migration command above if you have existing data

3. **Replace Files**
   - The resource now uses `client_fixed.lua` and `server_fixed.lua`
   - The `fxmanifest.lua` has been updated to use these files

4. **Clear Cache**
   - Delete your server cache folder
   - This ensures the new files are loaded properly

5. **Start Server**
   - Start your server normally
   - The resource should load with all fixes applied

## Testing the Fixes

1. **Team Persistence Test**
   - Join server and select a team
   - Disconnect and reconnect
   - You should spawn at your team base automatically

2. **Shop Test**
   - Press E near the peds at spawn
   - Your current money should display correctly
   - Try buying something to verify money deduction

3. **KOTH Zone Test**
   - Go to the quarry (KOTH zone)
   - Stand in the zone to start capturing
   - Check that points are awarded when captured

4. **Kill Reward Test**
   - Kill another player
   - You should receive $50 and 50 XP (double in zone)
   - Check `/checkstats` to verify

## Console Commands

- `/checkstats` - View your current stats
- `/loaddata` - Force reload your data from database
- `/checkdata` - Debug command to see raw player data

## Common Issues

### "Player data not loaded"
- This usually fixes itself after a few seconds
- Use `/loaddata` to force a reload
- Check that your database connection is working

### Team not saving
- Make sure the `current_team` column exists in database
- Check server console for any SQL errors

### Peds not spawning
- Peds spawn 3 seconds after resource starts
- Check console for any model loading errors
- Make sure ped models are valid

## Features Working

✅ Team selection on first join  
✅ Team persistence across sessions  
✅ Automatic spawn at team base  
✅ Working vehicle/weapon shops with database money  
✅ KOTH zone capture mechanics  
✅ Kill rewards and XP system  
✅ Level progression  
✅ Safe zones at team spawns  
✅ PVP enabled everywhere else  
✅ Real-time HUD updates  
✅ Map blips for all zones  

## Support

If you encounter any issues:
1. Check the server console for errors
2. Verify your database connection
3. Make sure all files are properly updated
4. Clear your FiveM cache if UI doesn't update
