-- REWORKED DEATH SYSTEM: Hold E to respawn with proper progress tracking and bleedout timer

local isDead = false
local playerTeam = nil
local hasSelectedTeam = false
local isRespawning = false
local deathTime = 0
local respawnHoldTime = 0
local respawnProgress = 0
local isHoldingE = false
local killerServerId = nil
local bleedoutDuration = 50 -- 50 seconds bleedout timer
local deathPosition = nil

local teamSpawns = {
    red = { x = 2238.15, y = 3788.91, z = 35.89, heading = 120.5 },
    blue = { x = 1323.77, y = 3143.33, z = 40.41, heading = 282.48 },
    green = { x = 1865.99, y = 2607.15, z = 45.67, heading = 276.45 }
}

local function playDeathAnimation()
    local playerPed = PlayerPedId()

    -- Use a more reliable death animation
    RequestAnimDict("missfinale_c2mcs_1")
    while not HasAnimDictLoaded("missfinale_c2mcs_1") do
        Citizen.Wait(1)
    end

    -- Play death animation
    TaskPlayAnim(playerPed, "missfinale_c2mcs_1", "fin_c2_mcs_1_camman", 8.0, -8.0, -1, 1, 0, false, false, false)

    -- Alternative: Use ragdoll for more realistic death
    SetPedToRagdoll(playerPed, 10000, 10000, 0, true, true, false)

    -- Ensure player stays down
    SetEntityHealth(playerPed, 0)
end

local function respawnAtTeamBase()
    if isRespawning then return end
    isRespawning = true

    local team = playerTeam or 'red'
    local spawn = teamSpawns[team]

    print('[KOTH DEATH] Respawning player at team base:', team)

    -- Hide death screen and reset variables
    SendNUIMessage({ action = 'hideDeathScreen' })

    Citizen.CreateThread(function()
        -- Properly resurrect the player
        local playerPed = PlayerPedId()

        -- Clear death state first
        ClearPedTasksImmediately(playerPed)
        ClearPedSecondaryTask(playerPed)

        -- Resurrect at team spawn
        NetworkResurrectLocalPlayer(spawn.x, spawn.y, spawn.z, spawn.heading, true, false)
        Citizen.Wait(100)

        -- Ensure resurrection worked
        playerPed = PlayerPedId()
        if IsEntityDead(playerPed) then
            NetworkResurrectLocalPlayer(spawn.x, spawn.y, spawn.z, spawn.heading, true, false)
            Citizen.Wait(100)
            playerPed = PlayerPedId()
        end

        -- Set position and properties
        SetEntityCoords(playerPed, spawn.x, spawn.y, spawn.z, false, false, false, true)
        SetEntityHeading(playerPed, spawn.heading)
        FreezeEntityPosition(playerPed, false)
        SetEntityInvincible(playerPed, false)
        SetPedCanRagdoll(playerPed, true)
        SetPedCanSwitchWeapon(playerPed, true)
        SetEntityCollision(playerPed, true, true)
        SetEntityHealth(playerPed, 200)

        -- Give weapon
        RemoveAllPedWeapons(playerPed, true)
        GiveWeaponToPed(playerPed, GetHashKey('WEAPON_PISTOL'), 250, false, true)

        -- Reset death state variables
        isDead = false
        isHoldingE = false
        respawnProgress = 0
        respawnHoldTime = 0
        deathPosition = nil

        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName(("Respawned at %s team base"):format(team))
        EndTextCommandThefeedPostTicker(false, true)

        isRespawning = false
        print('[KOTH DEATH] Respawn complete')
    end)
end

-- Handle killer name response from server
RegisterNetEvent('koth:playerNameResponse', function(playerId, playerName)
    if isDead then
        SendNUIMessage({
            action = 'showDeathScreen',
            killer = {
                id = playerId or '000000',
                name = playerName or 'Unknown'
            }
        })
    end
end)

RegisterNetEvent('koth:spawnPlayer', function(spawnData)
    -- Only process spawn events when not dead (initial team selection)
    if not isDead then
        print('[KOTH DEATH] Processing spawn event - not dead')

        -- Determine team from spawn location
        for team, spawn in pairs(teamSpawns) do
            if math.abs(spawnData.x - spawn.x) < 1.0 and math.abs(spawnData.y - spawn.y) < 1.0 then
                playerTeam = team
                hasSelectedTeam = true
                print('[KOTH DEATH] Player assigned to team:', team)
                break
            end
        end

        local playerPed = PlayerPedId()
        if IsEntityDead(playerPed) then
            NetworkResurrectLocalPlayer(spawnData.x, spawnData.y, spawnData.z, spawnData.heading or 0.0, true, false)
        end
        SetEntityCoords(playerPed, spawnData.x, spawnData.y, spawnData.z, false, false, false, true)
        SetEntityHeading(playerPed, spawnData.heading or 0.0)
        FreezeEntityPosition(playerPed, false)
        ClearPedTasksImmediately(playerPed)
        SendNUIMessage({ action = 'hideDeathScreen' })
    else
        print('[KOTH DEATH] Ignoring spawn event - player is dead')
    end
end)

-- Override playerSpawned to prevent team selection when dead
AddEventHandler('playerSpawned', function()
    print('[KOTH DEATH] playerSpawned event - isDead:', isDead, 'hasSelectedTeam:', hasSelectedTeam)

    -- If player is dead, prevent any spawn manager interference
    if isDead then
        print('[KOTH DEATH] Player is dead - blocking spawn manager')
        exports.spawnmanager:setAutoSpawn(false)

        -- Keep player at death position
        if deathPosition then
            local playerPed = PlayerPedId()
            SetEntityCoords(playerPed, deathPosition.x, deathPosition.y, deathPosition.z, false, false, false, true)
            FreezeEntityPosition(playerPed, true)
        end
        return
    end

    -- If player has already selected a team, don't show team selection again
    if hasSelectedTeam then
        print('[KOTH DEATH] Player has team - preventing team selection UI')
        exports.spawnmanager:setAutoSpawn(false)
        return
    end
end)

Citizen.CreateThread(function()
    exports.spawnmanager:setAutoSpawn(false)
    exports.spawnmanager:forceRespawn()

    while true do
        local playerPed = PlayerPedId()

        if IsEntityDead(playerPed) and not isDead then
            -- Player just died
            isDead = true
            deathTime = GetGameTimer()
            respawnHoldTime = 0
            respawnProgress = 0
            isHoldingE = false

            -- Store death position
            deathPosition = GetEntityCoords(playerPed)

            print('[KOTH DEATH] Player died at team:', playerTeam or 'unknown')

            -- Disable spawn manager to prevent auto-respawn
            exports.spawnmanager:setAutoSpawn(false)

            -- Get killer info
            local killerPed = GetPedSourceOfDeath(playerPed)
            killerServerId = nil

            if killerPed and killerPed ~= playerPed and IsPedAPlayer(killerPed) then
                killerServerId = GetPlayerServerId(NetworkGetPlayerIndexFromPed(killerPed))
            end

            -- Freeze player and play death animation
            FreezeEntityPosition(playerPed, true)
            SetEntityInvincible(playerPed, true)
            playDeathAnimation()

            -- Show death screen with killer info
            TriggerServerEvent('koth:getPlayerName', killerServerId)

        elseif isDead then
            -- Player is dead, handle respawn logic
            local playerPed = PlayerPedId()

            -- Keep playing death animation and freeze player
            if not IsEntityPlayingAnim(playerPed, "missfinale_c2mcs_1", "fin_c2_mcs_1_camman", 3) then
                playDeathAnimation()
            end
            FreezeEntityPosition(playerPed, true)
            SetEntityInvincible(playerPed, true)

            -- Calculate bleedout timer
            local elapsedTime = (GetGameTimer() - deathTime) / 1000
            local bleedoutRemaining = math.max(0, bleedoutDuration - math.floor(elapsedTime))

            -- Update death screen timer
            SendNUIMessage({
                action = 'updateBleedoutTimer',
                time = bleedoutRemaining
            })

            -- Auto-respawn if bleedout timer expires
            if bleedoutRemaining <= 0 and not isRespawning then
                respawnAtTeamBase()
            end

            -- Handle E key hold for respawn
            local currentlyHoldingE = IsControlPressed(0, 38) -- E key

            if currentlyHoldingE and not isHoldingE then
                -- Started holding E
                isHoldingE = true
                respawnHoldTime = GetGameTimer()
                respawnProgress = 0

            elseif currentlyHoldingE and isHoldingE then
                -- Continue holding E
                local holdDuration = (GetGameTimer() - respawnHoldTime) / 1000
                respawnProgress = math.min(holdDuration / 5.0, 1.0) -- 5 seconds to complete

                -- Update progress bar
                SendNUIMessage({
                    action = 'updateRespawnProgress',
                    progress = respawnProgress
                })

                -- Complete respawn if held for 5 seconds
                if respawnProgress >= 1.0 and not isRespawning then
                    respawnAtTeamBase()
                end

            elseif not currentlyHoldingE and isHoldingE then
                -- Released E key
                isHoldingE = false
                respawnProgress = 0

                -- Reset progress bar
                SendNUIMessage({
                    action = 'updateRespawnProgress',
                    progress = 0
                })
            end
        end

        Citizen.Wait(0)
    end
end)

-- Listen for NUI callback when hold E is complete
RegisterNUICallback('respawn', function(data, cb)
    if isDead and not isRespawning then
        respawnAtTeamBase()
    end
    cb('ok')
end)

-- Export function for other scripts to set player team
exports('setPlayerTeam', function(team)
    playerTeam = team
    hasSelectedTeam = true
    print('[KOTH DEATH] Team set via export:', team)
end)

-- Export function to get current team
exports('getPlayerTeam', function()
    return playerTeam
end)

-- Export function to check if player has selected team
exports('hasSelectedTeam', function()
    return hasSelectedTeam
end)

-- Control disabling thread for dead players
Citizen.CreateThread(function()
    while true do
        if isDead then
            local playerPed = PlayerPedId()

            -- Keep player frozen and invincible
            FreezeEntityPosition(playerPed, true)
            SetEntityInvincible(playerPed, true)

            -- Disable most controls except E key (which we handle separately)
            DisableControlAction(0, 1, true)   -- Mouse look
            DisableControlAction(0, 2, true)   -- Mouse look
            DisableControlAction(0, 7, true)   -- Camera
            DisableControlAction(0, 18, true)  -- Enter
            DisableControlAction(0, 22, true)  -- Jump
            DisableControlAction(0, 30, true)  -- Move left/right
            DisableControlAction(0, 31, true)  -- Move forward/back
            DisableControlAction(0, 32, true)  -- Move forward
            DisableControlAction(0, 33, true)  -- Move backward
            DisableControlAction(0, 34, true)  -- Move left
            DisableControlAction(0, 35, true)  -- Move right
            DisableControlAction(0, 37, true)  -- Weapon wheel
            DisableControlAction(0, 45, true)  -- Reload
            DisableControlAction(0, 157, true) -- Move up/down
            DisableControlAction(0, 158, true) -- Move up/down
            DisableControlAction(0, 160, true) -- Attack
            DisableControlAction(0, 164, true) -- Attack 2
            DisableControlAction(0, 165, true) -- Aim
            DisableControlAction(0, 176, true) -- Change weapon
            DisableControlAction(0, 249, true) -- Aim
            DisableControlAction(0, 284, true) -- Weapon wheel
            DisableControlAction(0, 311, true) -- Weapon wheel

            -- Don't disable E key (38) as we need it for respawn
        end
        Citizen.Wait(0)
    end
end)
