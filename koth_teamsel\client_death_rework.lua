-- REWORKED DEATH SYSTEM: Hold E to respawn with proper progress tracking and bleedout timer

local isDead = false
local playerTeam = nil
local hasSelectedTeam = false
local isRespawning = false
local deathTime = 0
local respawnHoldTime = 0
local respawnProgress = 0
local isHoldingE = false
local killerServerId = nil
local bleedoutDuration = 50 -- 50 seconds bleedout timer

local teamSpawns = {
    red = { x = 2238.15, y = 3788.91, z = 35.89, heading = 120.5 },
    blue = { x = 1323.77, y = 3143.33, z = 40.41, heading = 282.48 },
    green = { x = 1865.99, y = 2607.15, z = 45.67, heading = 276.45 }
}

local function playDeathAnimation()
    local playerPed = PlayerPedId()
    RequestAnimDict("dead")
    while not HasAnimDictLoaded("dead") do
        Citizen.Wait(1)
    end
    TaskPlayAnim(playerPed, "dead", "dead_a", 8.0, -8.0, -1, 1, 0, false, false, false)
    SetPedToRagdoll(playerPed, 1000, 1000, 0, true, true, false)
end

local function respawnAtTeamBase()
    if isRespawning then return end
    isRespawning = true

    local team = playerTeam or 'red'
    local spawn = teamSpawns[team]

    -- Hide death screen and reset variables
    SendNUIMessage({ action = 'hideDeathScreen' })
    isDead = false
    isHoldingE = false
    respawnProgress = 0
    respawnHoldTime = 0

    Citizen.CreateThread(function()
        NetworkResurrectLocalPlayer(spawn.x, spawn.y, spawn.z, spawn.heading, true, false)
        Citizen.Wait(200)
        local playerPed = PlayerPedId()
        if IsEntityDead(playerPed) then
            NetworkResurrectLocalPlayer(spawn.x, spawn.y, spawn.z, spawn.heading, true, false)
            Citizen.Wait(100)
            playerPed = PlayerPedId()
        end
        ClearPedTasksImmediately(playerPed)
        ClearPedSecondaryTask(playerPed)
        SetEntityCoords(playerPed, spawn.x, spawn.y, spawn.z, false, false, false, true)
        SetEntityHeading(playerPed, spawn.heading)
        FreezeEntityPosition(playerPed, false)
        SetEntityInvincible(playerPed, false)
        SetPedCanRagdoll(playerPed, true)
        SetPedCanSwitchWeapon(playerPed, true)
        SetEntityCollision(playerPed, true, true)
        RemoveAllPedWeapons(playerPed, true)
        GiveWeaponToPed(playerPed, GetHashKey('WEAPON_PISTOL'), 250, false, true)

        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName(("Respawned at %s team base"):format(team))
        EndTextCommandThefeedPostTicker(false, true)

        isRespawning = false
    end)
end

-- Handle killer name response from server
RegisterNetEvent('koth:playerNameResponse', function(playerId, playerName)
    if isDead then
        SendNUIMessage({
            action = 'showDeathScreen',
            killer = {
                id = playerId or '000000',
                name = playerName or 'Unknown'
            }
        })
    end
end)

RegisterNetEvent('koth:spawnPlayer', function(spawnData)
    if not isDead then
        for team, spawn in pairs(teamSpawns) do
            if math.abs(spawnData.x - spawn.x) < 1.0 and math.abs(spawnData.y - spawn.y) < 1.0 then
                playerTeam = team
                hasSelectedTeam = true
                break
            end
        end
        local playerPed = PlayerPedId()
        if IsEntityDead(playerPed) then
            NetworkResurrectLocalPlayer(spawnData.x, spawnData.y, spawnData.z, spawnData.heading or 0.0, true, false)
        end
        SetEntityCoords(playerPed, spawnData.x, spawnData.y, spawnData.z, false, false, false, true)
        SetEntityHeading(playerPed, spawnData.heading or 0.0)
        FreezeEntityPosition(playerPed, false)
        ClearPedTasksImmediately(playerPed)
        isDead = false
        SendNUIMessage({ action = 'hideDeathScreen' })
    end
end)

AddEventHandler('playerSpawned', function()
    if hasSelectedTeam and isDead then
        exports.spawnmanager:setAutoSpawn(false)
        return
    end
end)

Citizen.CreateThread(function()
    exports.spawnmanager:setAutoSpawn(false)
    exports.spawnmanager:forceRespawn()

    while true do
        local playerPed = PlayerPedId()

        if IsEntityDead(playerPed) and not isDead then
            -- Player just died
            isDead = true
            deathTime = GetGameTimer()
            respawnHoldTime = 0
            respawnProgress = 0
            isHoldingE = false

            -- Get killer info
            local killerPed = GetPedSourceOfDeath(playerPed)
            killerServerId = nil

            if killerPed and killerPed ~= playerPed and IsPedAPlayer(killerPed) then
                killerServerId = GetPlayerServerId(NetworkGetPlayerIndexFromPed(killerPed))
            end

            -- Freeze player and play death animation
            FreezeEntityPosition(playerPed, true)
            playDeathAnimation()

            -- Show death screen with killer info
            TriggerServerEvent('koth:getPlayerName', killerServerId)

        elseif isDead then
            -- Player is dead, handle respawn logic
            local playerPed = PlayerPedId()

            -- Keep playing death animation and freeze player
            if not IsEntityPlayingAnim(playerPed, "dead", "dead_a", 3) then
                playDeathAnimation()
            end
            FreezeEntityPosition(playerPed, true)

            -- Calculate bleedout timer
            local elapsedTime = (GetGameTimer() - deathTime) / 1000
            local bleedoutRemaining = math.max(0, bleedoutDuration - math.floor(elapsedTime))

            -- Update death screen timer
            SendNUIMessage({
                action = 'updateBleedoutTimer',
                time = bleedoutRemaining
            })

            -- Auto-respawn if bleedout timer expires
            if bleedoutRemaining <= 0 and not isRespawning then
                respawnAtTeamBase()
            end

            -- Handle E key hold for respawn
            local currentlyHoldingE = IsControlPressed(0, 38) -- E key

            if currentlyHoldingE and not isHoldingE then
                -- Started holding E
                isHoldingE = true
                respawnHoldTime = GetGameTimer()
                respawnProgress = 0

            elseif currentlyHoldingE and isHoldingE then
                -- Continue holding E
                local holdDuration = (GetGameTimer() - respawnHoldTime) / 1000
                respawnProgress = math.min(holdDuration / 5.0, 1.0) -- 5 seconds to complete

                -- Update progress bar
                SendNUIMessage({
                    action = 'updateRespawnProgress',
                    progress = respawnProgress
                })

                -- Complete respawn if held for 5 seconds
                if respawnProgress >= 1.0 and not isRespawning then
                    respawnAtTeamBase()
                end

            elseif not currentlyHoldingE and isHoldingE then
                -- Released E key
                isHoldingE = false
                respawnProgress = 0

                -- Reset progress bar
                SendNUIMessage({
                    action = 'updateRespawnProgress',
                    progress = 0
                })
            end
        end

        Citizen.Wait(0)
    end
end)

-- Listen for NUI callback when hold E is complete
RegisterNUICallback('respawn', function(data, cb)
    if isDead and not isRespawning then
        respawnAtTeamBase()
    end
    cb('ok')
end)

Citizen.CreateThread(function()
    while true do
        if isDead then
            local playerPed = PlayerPedId()
            if not IsEntityPlayingAnim(playerPed, "dead", "dead_a", 3) then
                FreezeEntityPosition(playerPed, true)
            end
            DisableControlAction(0, 7, true)
            DisableControlAction(0, 18, true)
            DisableControlAction(0, 22, true)
            DisableControlAction(0, 176, true)
            DisableControlAction(0, 249, true)
            DisableControlAction(0, 45, true)
            DisableControlAction(0, 284, true)
            DisableControlAction(0, 311, true)
            DisableControlAction(0, 30, true)
            DisableControlAction(0, 31, true)
            DisableControlAction(0, 32, true)
            DisableControlAction(0, 33, true)
            DisableControlAction(0, 34, true)
            DisableControlAction(0, 35, true)
            DisableControlAction(0, 1, true)
            DisableControlAction(0, 2, true)
            DisableControlAction(0, 37, true)
            DisableControlAction(0, 157, true)
            DisableControlAction(0, 158, true)
            DisableControlAction(0, 160, true)
            DisableControlAction(0, 164, true)
            DisableControlAction(0, 165, true)
        end
        Citizen.Wait(0)
    end
end)
