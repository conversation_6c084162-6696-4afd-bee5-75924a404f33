-- UI FIX: Force show team selection UI
RegisterNetEvent('koth:forceShowTeamSelect', function()
  print('[KOTH] Force showing team selection UI')
  
  -- Send multiple attempts to ensure UI shows
  for i = 1, 3 do
    Citizen.SetTimeout(i * 100, function()
      SendNUIMessage({ 
        action = 'showTeamSelect', 
        counts = { red = 0, blue = 0, green = 0 }
      })
      
      -- Also send a direct show overlay message
      SendNUIMessage({ 
        action = 'forceShowOverlay'
      })
    end)
  end
  
  -- Set focus with delay
  Citizen.SetTimeout(500, function()
    SetNuiFocus(true, true)
    print('[KOTH] NUI Focus set to true')
  end)
end)

-- Test command to verify UI works
RegisterCommand('testui', function()
  print('[KOTH] Testing UI display')
  TriggerEvent('koth:forceShowTeamSelect')
end, false)

-- Auto-trigger on spawn
AddEventHandler('playerSpawned', function()
  print('[KOTH] Player spawned - forcing UI display')
  Citizen.SetTimeout(1000, function()
    TriggerEvent('koth:forceShowTeamSelect')
  end)
end)
