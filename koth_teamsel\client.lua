print('[KOTH] Client loading...')

-- TEAM SPAWN COORDS
local teamSpawns = {
  red   = { x=2238.15, y=3788.91, z=35.89, heading=120.5 },
  blue  = { x=1323.77, y=3143.33, z=40.41, heading=282.48 },
  green = { x=1865.99, y=2607.15, z=45.67, heading=276.45 },
}

-- Player variables
local playerTeam = nil
local playerStats = nil
local hasSelectedTeam = false
local weaponShopOpened = false

-- Request team counts and show UI
AddEventHandler('playerSpawned', function()
  print('[KOTH] Player spawned event triggered')
  
  -- Show team selection immediately
  Citizen.SetTimeout(100, function()
    print('[KOTH] Showing team selection UI...')
    
    -- Force show team selection
    if not hasSelectedTeam then
      print('[KOTH] Requesting team counts...')
      TriggerServerEvent('koth:requestCounts')
      
      -- Also freeze player until they select a team
      local playerPed = PlayerPedId()
      FreezeEntityPosition(playerPed, true)
      SetEntityInvincible(playerPed, true)
    end
    
    -- Request player data in parallel
    TriggerServerEvent('koth:requestPlayerData')
  end)
end)

-- Also trigger immediately when script loads (for restart scenarios)
Citizen.CreateThread(function()
  Citizen.Wait(1000) -- Wait for game to initialize
  
  if not hasSelectedTeam then
    print('[KOTH] Initial load - requesting team selection')
    TriggerServerEvent('koth:requestCounts')
    
    -- Freeze player
    local playerPed = PlayerPedId()
    FreezeEntityPosition(playerPed, true)
    SetEntityInvincible(playerPed, true)
  end
end)

-- Also trigger on resource start for testing
AddEventHandler('onClientResourceStart', function(res)
  if GetCurrentResourceName() ~= res then return end
  
  -- Wait a bit for player to fully load
  Citizen.SetTimeout(1000, function()
    if not hasSelectedTeam then
      print('[KOTH] Resource started, requesting team counts...')
      TriggerServerEvent('koth:requestCounts')
    end
    
    -- Request player data
    TriggerServerEvent('koth:requestPlayerData')
  end)
end)

-- Handle player data updates from server
RegisterNetEvent('koth:updatePlayerData', function(data)
  print('[KOTH] Received player data update')
  playerStats = data
  
  -- Update HUD with real player data
  if playerStats then
    SendNUIMessage({
      action = 'updateHUD',
      playerData = {
        name = playerStats.player_name or GetPlayerName(PlayerId()),
        money = playerStats.money or 0,
        level = playerStats.level or 1,
        xp = playerStats.xp or 0,
        kills = playerStats.kills or 0,
        deaths = playerStats.deaths or 0
      }
    })
  end
end)

-- Handle spawn event from server
RegisterNetEvent('koth:spawnPlayer', function(spawnData)
  print('[KOTH] Spawning player at team location')
  
  local playerPed = PlayerPedId()
  
  -- Set spawn position
  SetEntityCoords(playerPed, spawnData.x, spawnData.y, spawnData.z, false, false, false, true)
  SetEntityHeading(playerPed, spawnData.heading or 0.0)
  
  -- Ensure player is on ground and unfrozen
  SetEntityCollision(playerPed, true, true)
  FreezeEntityPosition(playerPed, false)
  SetEntityInvincible(playerPed, false)
  
  -- Give basic weapon
  GiveWeaponToPed(playerPed, GetHashKey('WEAPON_PISTOL'), 250, false, true)
  
  -- Initialize HUD
  SendNUIMessage({ 
    action = 'initHUD',
    playerData = playerStats
  })
  
  -- Enable PVP
  SetCanAttackFriendly(playerPed, true, false)
  NetworkSetFriendlyFireOption(true)
  
  -- Make sure player can move
  ClearPedTasksImmediately(playerPed)
  
  print('[KOTH] Player spawned successfully and unfrozen')
end)

-- Receive counts and show team select
RegisterNetEvent('koth:updateCounts', function(counts)
  print('[KOTH] Received counts, showing UI')
  print('[KOTH] Counts data:', json.encode(counts or {}))
  
  -- Force show the UI even if counts is nil
  SendNUIMessage({ 
    action = 'showTeamSelect', 
    counts = counts or { red = 0, blue = 0, green = 0 }
  })
  SetNuiFocus(true, true)
  
  -- Also try to show the HUD
  SendNUIMessage({ action = 'initHUD' })
  
  print('[KOTH] NUI message sent and focus set')
end)

-- Team selection callback
RegisterNUICallback('selectTeam', function(data, cb)
  print('[KOTH] Team selected:', data.team or 'none')
  if data and data.team then
    playerTeam = data.team
    hasSelectedTeam = true
    
    -- Store team for death system
    SetResourceKvp('playerTeam', data.team)
    TriggerEvent('koth:teamSelected', data.team)
    
    TriggerServerEvent('koth:pickTeam', data.team)
    SendNUIMessage({ action='hideAll' })
    SetNuiFocus(false, false)
  end
  cb('ok')
end)

-- Receive fresh vehicle shop data with money
RegisterNetEvent('koth:showVehicleShopWithMoney', function(data)
  print('[KOTH] Received vehicle shop data with money:', data.money)
  
  SendNUIMessage({
    action = 'showMenu',
    type = 'vehicles',
    items = data.vehicles,
    money = data.money
  })
  SetNuiFocus(true, true)
end)

-- Receive fresh class shop data with money and level
RegisterNetEvent('koth:showClassShopWithData', function(data)
  print('[KOTH] Received class shop data with money:', data.money, 'level:', data.level)
  
  -- Add locked status based on player level
  for i, class in ipairs(data.classes) do
    class.locked = data.level < class.requiredLevel
  end
  
  SendNUIMessage({
    action = 'showMenu',
    type = 'classes',
    items = data.classes,
    playerLevel = data.level,
    money = data.money
  })
  SetNuiFocus(true, true)
end)

-- Receive fresh weapon shop data with money
RegisterNetEvent('koth:showWeaponShopWithMoney', function(data)
  print('[KOTH] Received weapon shop data with money:', data.money)
  weaponShopOpened = true
  
  SendNUIMessage({
    action = 'showWeaponSelect',
    class = data.class,
    weapons = data.weapons,
    money = data.money
  })
end)

-- Vehicle menu - triggered by ped interaction
RegisterNetEvent('koth:openVehicleMenu', function(items)
  print('[KOTH] Opening vehicle menu')

  -- Use provided items or comprehensive vehicle data
  local vehicles = items or {
    { name = 'Blista', cost = 8000, rent = 1200, img = 'images/vehicles/blista.png' },
    { name = 'Futo', cost = 12000, rent = 1800, img = 'images/vehicles/futo.png' },
    { name = 'Sultan', cost = 15000, rent = 2200, img = 'images/vehicles/sultan.png' },
    { name = 'Elegy', cost = 18000, rent = 2700, img = 'images/vehicles/elegy.png' },
    { name = 'Kuruma', cost = 22000, rent = 3300, img = 'images/vehicles/kuruma.png' },
    { name = 'Armored Kuruma', cost = 35000, rent = 5200, img = 'images/vehicles/kuruma_armored.png' },
    { name = 'Insurgent', cost = 45000, rent = 6700, img = 'images/vehicles/insurgent.png' },
    { name = 'Technical', cost = 28000, rent = 4200, img = 'images/vehicles/technical.png' },
    { name = 'Sandking XL', cost = 32000, rent = 4800, img = 'images/vehicles/sandking.png' },
    { name = 'Mesa', cost = 25000, rent = 3700, img = 'images/vehicles/mesa.png' },
    { name = 'Buzzard', cost = 85000, rent = 12700, img = 'images/vehicles/buzzard.png' },
    { name = 'Savage', cost = 120000, rent = 18000, img = 'images/vehicles/savage.png' },
    { name = 'Rhino Tank', cost = 150000, rent = 22500, img = 'images/vehicles/rhino.png' },
    { name = 'Hydra', cost = 200000, rent = 30000, img = 'images/vehicles/hydra.png' }
  }

  -- Request fresh data from server
  TriggerServerEvent('koth:getMoneyForVehicleShop', vehicles)
end)

-- Class menu - triggered by ped interaction
RegisterNetEvent('koth:openClassMenu', function(items)
  print('[KOTH] Opening class menu')

  -- Use provided items or exact classes from the image
  local classes = items or {
    {
      id = 'assault',
      name = 'Assault',
      unlock = 'Unlocked',
      img = 'images/classes/assault.png',
      requiredLevel = 1
    },
    {
      id = 'medic',
      name = 'Medic',
      unlock = 'Unlock at level 5',
      img = 'images/classes/medic.png',
      requiredLevel = 5
    },
    {
      id = 'engineer',
      name = 'Engineer',
      unlock = 'Unlock at level 15',
      img = 'images/classes/engineer.png',
      requiredLevel = 15
    },
    {
      id = 'heavy',
      name = 'Heavy',
      unlock = 'Unlock at level 25',
      img = 'images/classes/heavy.png',
      requiredLevel = 25
    },
    {
      id = 'scout',
      name = 'Scout',
      unlock = 'Unlock at level 40',
      img = 'images/classes/scout.png',
      requiredLevel = 40
    }
  }

  -- Request fresh data from server
  TriggerServerEvent('koth:getDataForClassShop', classes)
end)

-- Vehicle purchase callbacks
RegisterNUICallback('buyVehicle', function(data, cb)
  print('[KOTH] Buying vehicle:', data.name or 'none')
  if data and data.name then
    -- Send price data with the purchase
    TriggerServerEvent('koth:buyVehicle', {
      name = data.name,
      price = data.cost or 0
    })
  end
  SendNUIMessage({ action='hideAll' })
  SetNuiFocus(false, false)
  cb('ok')
end)

RegisterNUICallback('rentVehicle', function(data, cb)
  print('[KOTH] Renting vehicle:', data.name or 'none')
  if data and data.name then
    -- Send price data with the rental
    TriggerServerEvent('koth:rentVehicle', {
      name = data.name,
      price = data.rent or 0
    })
  end
  SendNUIMessage({ action='hideAll' })
  SetNuiFocus(false, false)
  cb('ok')
end)

-- Class selection callback - now shows weapon selection
RegisterNUICallback('selectClass', function(data, cb)
  print('[KOTH] Class selected:', data.id or 'none')
  if data and data.id then
    -- Check if player has required level for this class
    local playerLevel = playerStats and playerStats.level or 1
    local requiredLevels = {
      assault = 1,
      medic = 5,
      engineer = 15,
      heavy = 25,
      scout = 40
    }

    local requiredLevel = requiredLevels[data.id] or 1
    if playerLevel < requiredLevel then
      -- Show error message
      BeginTextCommandThefeedPost("STRING")
      AddTextComponentSubstringPlayerName(('Class locked! Requires level %d (you are level %d)'):format(requiredLevel, playerLevel))
      EndTextCommandThefeedPostTicker(false, true)
      cb('ok')
      return
    end
    -- Define weapons exactly as shown in the image with prices
    local allWeapons = {
      { weapon = 'WEAPON_STONE_HATCHET', name = 'Stone Hatchet', price = 150, img = 'images/guns/stone_hatchet.png' },
      { weapon = 'WEAPON_CROWBAR', name = 'Crowbar', price = 200, img = 'images/guns/crowbar.png' },
      { weapon = 'WEAPON_HATCHET', name = 'Hatchet', price = 250, img = 'images/guns/hatchet.png' },
      { weapon = 'WEAPON_PISTOL', name = 'Pistol', price = 500, img = 'images/guns/pistol.png' },
      { weapon = 'WEAPON_MICROSMG', name = 'Micro SMG', price = 800, img = 'images/guns/micro_smg.png' },
      { weapon = 'WEAPON_COMBATPDW', name = 'Combat PDW', price = 1200, img = 'images/guns/combat_pdw.png' },
      { weapon = 'WEAPON_ASSAULTSMG', name = 'Assault SMG', price = 1500, img = 'images/guns/assault_smg.png' },
      { weapon = 'WEAPON_BULLPUPRIFLE', name = 'Bullpup Rifle', price = 2500, img = 'images/guns/bullpup_rifle.png' },
      { weapon = 'WEAPON_HEAVYRIFLE', name = 'Heavy Rifle', price = 3000, img = 'images/guns/heavy_rifle.png' },
      { weapon = 'WEAPON_ASSAULTRIFLE', name = 'Assault Rifle', price = 3500, img = 'images/guns/assault_rifle.png' },
      { weapon = 'WEAPON_SPECIALCARBINE', name = 'Special Carbine', price = 4000, img = 'images/guns/special_carbine.png' },
      { weapon = 'WEAPON_SPECIALCARBINE_MK2', name = 'Special Carbine Mk2', price = 5000, img = 'images/guns/special_carbine_mk2.png' },
      { weapon = 'WEAPON_CARBINERIFLE_MK2', name = 'Carbine Rifle Mk2', price = 5500, img = 'images/guns/carbine_rifle_mk2.png' }
    }

    -- Request fresh money data directly from server for weapon shop
    print('[KOTH] Requesting fresh money data for weapon shop...')
    TriggerServerEvent('koth:getMoneyForWeaponShop', data.id, allWeapons)
  end
  cb('ok')
end)

-- Weapon selection callback
RegisterNUICallback('selectWeapon', function(data, cb)
  print('[KOTH] Weapon selected:', data.weapon or 'none', 'for class:', data.class or 'none', 'price:', data.price or 'none')
  if data and data.weapon and data.class then
    TriggerServerEvent('koth:selectLoadout', data.class, data.weapon, data.price)
  end
  SendNUIMessage({ action='hideAll' })
  SetNuiFocus(false, false)
  cb('ok')
end)

-- Receive weapon from server and give to player
RegisterNetEvent('koth:giveWeapon', function(weapon, classId, price)
  print(('[KOTH] Receiving weapon: %s for class: %s (price: $%s)'):format(weapon or 'none', classId or 'none', price or 'free'))

  if weapon then
    local playerPed = PlayerPedId()

    -- Remove all weapons first
    RemoveAllPedWeapons(playerPed, true)

    -- Give the selected weapon with ammo
    GiveWeaponToPed(playerPed, GetHashKey(weapon), 250, false, true)

    -- Set as current weapon
    SetCurrentPedWeapon(playerPed, GetHashKey(weapon), true)

    print(('[KOTH] Successfully equipped %s'):format(weapon))

    -- Show notification with price
    BeginTextCommandThefeedPost("STRING")
    local weaponName = weapon:gsub('WEAPON_', ''):gsub('_', ' ')
    local message = ('Purchased %s for $%s'):format(weaponName, price or '0')
    AddTextComponentSubstringPlayerName(message)
    EndTextCommandThefeedPostTicker(false, true)
  end
end)

-- Purchase result handler
RegisterNetEvent('koth:purchaseResult', function(success, message)
  print(('[KOTH] Purchase result: %s - %s'):format(success and 'SUCCESS' or 'FAILED', message))

  -- Show notification
  BeginTextCommandThefeedPost("STRING")
  AddTextComponentSubstringPlayerName(message)
  EndTextCommandThefeedPostTicker(false, true)

  -- If purchase failed, keep the shop open
  if not success then
    -- You could add specific UI feedback here
    print('[KOTH] Purchase failed, keeping shop open')
  end
end)

-- Close menu callback
RegisterNUICallback('closeMenu', function(data, cb)
  print('[KOTH] Menu closed')
  SendNUIMessage({ action='hideAll' })
  SetNuiFocus(false, false)
  cb('ok')
end)

-- Vehicle spawning handler
RegisterNetEvent('koth:spawnVehicle', function(vehicleName, purchaseType, price)
  print(('[KOTH] Spawning vehicle: %s (%s for $%s)'):format(vehicleName or 'none', purchaseType or 'unknown', price or '0'))

  if vehicleName then
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local playerHeading = GetEntityHeading(playerPed)

    -- Vehicle name mapping (simplified)
    local vehicleModels = {
      ['Blista'] = 'blista',
      ['Futo'] = 'futo',
      ['Sultan'] = 'sultan',
      ['Elegy'] = 'elegy2',
      ['Kuruma'] = 'kuruma',
      ['Armored Kuruma'] = 'kuruma2',
      ['Insurgent'] = 'insurgent',
      ['Technical'] = 'technical',
      ['Sandking XL'] = 'sandking',
      ['Mesa'] = 'mesa',
      ['Buzzard'] = 'buzzard2',
      ['Savage'] = 'savage',
      ['Rhino Tank'] = 'rhino',
      ['Hydra'] = 'hydra'
    }

    local modelName = vehicleModels[vehicleName] or vehicleName:lower()
    local modelHash = GetHashKey(modelName)

    -- Request model
    RequestModel(modelHash)
    local timeout = GetGameTimer() + 5000
    while not HasModelLoaded(modelHash) and GetGameTimer() < timeout do
      Citizen.Wait(1)
    end

    if HasModelLoaded(modelHash) then
      -- Find spawn position (in front of player)
      local spawnX = playerCoords.x + math.cos(math.rad(playerHeading)) * 5.0
      local spawnY = playerCoords.y + math.sin(math.rad(playerHeading)) * 5.0

      -- Get proper ground Z coordinate to prevent falling through map
      local foundGround, groundZ = GetGroundZFor_3dCoord(spawnX, spawnY, playerCoords.z + 50.0, false)
      local spawnZ = foundGround and (groundZ + 1.0) or (playerCoords.z + 1.0)

      -- Create vehicle
      local vehicle = CreateVehicle(modelHash, spawnX, spawnY, spawnZ, playerHeading, true, false)

      if DoesEntityExist(vehicle) then
        -- Ensure vehicle is properly placed on ground
        SetEntityCoords(vehicle, spawnX, spawnY, spawnZ, false, false, false, true)
        SetVehicleOnGroundProperly(vehicle)

        -- Set player as owner
        SetVehicleHasBeenOwnedByPlayer(vehicle, true)
        SetEntityAsMissionEntity(vehicle, true, true)

        -- Ensure vehicle has proper collision with ground
        SetEntityCollision(vehicle, true, true)

        -- Wait a moment for physics to settle
        Citizen.Wait(100)

        -- Put player in vehicle
        TaskWarpPedIntoVehicle(playerPed, vehicle, -1)

        print(('[KOTH] Successfully spawned %s at ground level'):format(vehicleName))

        -- Show notification
        BeginTextCommandThefeedPost("STRING")
        local message = ('%s %s for $%s'):format(purchaseType == 'rent' and 'Rented' or 'Purchased', vehicleName, price or '0')
        AddTextComponentSubstringPlayerName(message)
        EndTextCommandThefeedPostTicker(false, true)
      else
        print(('[KOTH] Failed to create vehicle: %s'):format(vehicleName))
      end

      SetModelAsNoLongerNeeded(modelHash)
    else
      print(('[KOTH] Failed to load model: %s'):format(modelName))
    end
  end
end)

-- PED SPAWNING SYSTEM
local spawnedPeds = {}

-- Ped configurations for each team
local pedSpawns = {
  red = {
    { model = `s_m_y_marine_01`,   offset = vector3( 3.0,  2.0, 0.0), text = 'Click [E] To Open Classes Menu',  event = 'koth:openClassMenu'  },
    { model = `s_m_m_autoshop_02`, offset = vector3(-3.0, -2.0, 0.0), text = 'Click [E] To Open Vehicle Menu', event = 'koth:openVehicleMenu' },
  },
  blue = {
    { model = `s_m_y_marine_01`,   offset = vector3( 3.0,  2.0, 0.0), text = 'Click [E] To Open Classes Menu',  event = 'koth:openClassMenu'  },
    { model = `s_m_m_autoshop_02`, offset = vector3(-3.0, -2.0, 0.0), text = 'Click [E] To Open Vehicle Menu', event = 'koth:openVehicleMenu' },
  },
  green = {
    { model = `s_m_y_marine_01`,   offset = vector3( 3.0,  2.0, 0.0), text = 'Click [E] To Open Classes Menu',  event = 'koth:openClassMenu'  },
    { model = `s_m_m_autoshop_02`, offset = vector3(-3.0, -2.0, 0.0), text = 'Click [E] To Open Vehicle Menu', event = 'koth:openVehicleMenu' },
  },
}

-- 3D text helper function
local function Draw3DText(x, y, z, text)
  local onScreen, _x, _y = World3dToScreen2d(x, y, z + 1.0)
  if onScreen then
    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextCentre(true)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    AddTextComponentString(text)
    DrawText(_x, _y)
    local factor = (#text) / 370
    DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 0, 0, 0, 120)
  end
end

-- Spawn peds when resource starts
AddEventHandler('onClientResourceStart', function(res)
  if GetCurrentResourceName() ~= res then return end

  Citizen.CreateThread(function()
    print('[KOTH] Starting ped spawning...')
    Citizen.Wait(3000) -- Wait for everything to load

    for team, list in pairs(pedSpawns) do
      local sv = teamSpawns[team]
      if not sv then
        print('[KOTH] WARNING: No spawn data for team ' .. team)
      else
        local basePos = vector3(sv.x, sv.y, sv.z)
        print(('[KOTH] Spawning peds for team %s at %.2f, %.2f, %.2f'):format(team, basePos.x, basePos.y, basePos.z))

        for _, info in ipairs(list) do
          -- Validate model
          if not (IsModelValid(info.model) and IsModelInCdimage(info.model)) then
            print(('[KOTH] SKIP: Invalid model %s for team %s'):format(info.model, team))
          else
            -- Load model
            RequestModel(info.model)
            local timeout = GetGameTimer() + 5000
            while not HasModelLoaded(info.model) and GetGameTimer() < timeout do
              Citizen.Wait(1)
            end

            if not HasModelLoaded(info.model) then
              print(('[KOTH] FAIL: Could not load model %s'):format(info.model))
            else
              -- Find ground level
              local spawnX, spawnY = basePos.x + info.offset.x, basePos.y + info.offset.y
              local foundGround, groundZ = GetGroundZFor_3dCoord(spawnX, spawnY, basePos.z + 50.0, false)
              local finalZ = foundGround and groundZ or basePos.z

              -- Spawn ped
              local ped = CreatePed(4, info.model, spawnX, spawnY, finalZ, sv.heading or 0.0, false, true)

              if DoesEntityExist(ped) then
                SetEntityHeading(ped, sv.heading or 0.0)
                FreezeEntityPosition(ped, true)
                SetEntityInvincible(ped, true)
                SetBlockingOfNonTemporaryEvents(ped, true)

                table.insert(spawnedPeds, {
                  ped = ped,
                  text = info.text,
                  event = info.event
                })

                print(('[KOTH] SUCCESS: Spawned %s ped for team %s'):format(info.model, team))
              else
                print(('[KOTH] FAIL: CreatePed failed for %s'):format(info.model))
              end

              SetModelAsNoLongerNeeded(info.model)
            end
          end
        end
      end
    end

    print(('[KOTH] Ped spawning complete. Total peds: %d'):format(#spawnedPeds))
  end)
end)

-- Interaction loop for peds
Citizen.CreateThread(function()
  while true do
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)

    for _, info in ipairs(spawnedPeds) do
      if DoesEntityExist(info.ped) then
        local pedCoords = GetEntityCoords(info.ped)
        local distance = #(playerCoords - pedCoords)

        if distance < 10.0 then
          Draw3DText(pedCoords.x, pedCoords.y, pedCoords.z, info.text)

          if distance < 1.5 and IsControlJustReleased(0, 38) then -- E key
            print(('[KOTH] Player interacted with ped: %s'):format(info.event))
            TriggerEvent(info.event)
          end
        end
      end
    end

    Citizen.Wait(0)
  end
end)

-- SAFE ZONE SYSTEM
local inSafeZone = false
local safeZoneRadius = 25.0 -- Radius of safe zones
local teamBlips = {} -- Store map blips

-- Zone colors for each team
local zoneColors = {
  red = { r = 255, g = 0, b = 0, a = 50 },
  blue = { r = 0, g = 100, b = 255, a = 50 },
  green = { r = 0, g = 255, b = 0, a = 50 }
}

-- Map blip colors (GTA blip color IDs)
local blipColors = {
  red = 1,    -- Red
  blue = 3,   -- Blue
  green = 2   -- Green
}

-- Safe zone check thread
Citizen.CreateThread(function()
  while true do
    if playerTeam then
      local playerPed = PlayerPedId()
      local playerCoords = GetEntityCoords(playerPed)
      local wasInSafeZone = inSafeZone
      inSafeZone = false
      
      -- Check all team spawns for safe zones
      for team, spawn in pairs(teamSpawns) do
        local distance = #(playerCoords - vector3(spawn.x, spawn.y, spawn.z))
        
        if distance <= safeZoneRadius then
          inSafeZone = true
          
          -- Disable PVP in safe zones
          SetCanAttackFriendly(playerPed, false, false)
          NetworkSetFriendlyFireOption(false)
          
          -- Draw safe zone marker
          DrawMarker(1, spawn.x, spawn.y, spawn.z - 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 
            safeZoneRadius * 2.0, safeZoneRadius * 2.0, 5.0, 
            zoneColors[team].r, zoneColors[team].g, zoneColors[team].b, zoneColors[team].a, 
            false, false, 2, false, nil, nil, false)
          
          -- Show safe zone notification
          if not wasInSafeZone then
            BeginTextCommandThefeedPost("STRING")
            AddTextComponentSubstringPlayerName("Entered Safe Zone - PVP Disabled")
            EndTextCommandThefeedPostTicker(false, true)
          end
          
          break
        end
      end
      
      -- Re-enable PVP when leaving safe zone
      if not inSafeZone and wasInSafeZone then
        SetCanAttackFriendly(playerPed, true, false)
        NetworkSetFriendlyFireOption(true)
        
        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName("Left Safe Zone - PVP Enabled")
        EndTextCommandThefeedPostTicker(false, true)
      end
    end
    
    Citizen.Wait(100)
  end
end)

-- KOTH ZONE SYSTEM
local kothZone = {
  x = 2842.4216,
  y = 2864.8088,
  z = 62.5975,
  radius = 150.0, -- Massive zone radius
  controllingTeam = nil, -- 'red', 'blue', 'green', or nil for neutral
  captureProgress = 0.0, -- 0.0 to 100.0
  captureRate = 1.0, -- Points per second when capturing
  playersInZone = {}, -- Track players in zone by team
  blip = nil, -- Map blip for the zone
  centerBlip = nil -- Center marker blip
}

local inKothZone = false
local kothZoneColors = {
  neutral = { r = 128, g = 128, b = 128, a = 50 }, -- Grey
  red = { r = 255, g = 0, b = 0, a = 50 },
  blue = { r = 0, g = 100, b = 255, a = 50 },
  green = { r = 0, g = 255, b = 0, a = 50 }
}

local kothBlipColors = {
  neutral = 8, -- Grey
  red = 1,     -- Red
  blue = 3,    -- Blue
  green = 2    -- Green
}

-- Create KOTH zone blip
local function createKothBlip()
  print('[KOTH] Creating KOTH zone blip...')

  -- Create radius blip
  kothZone.blip = AddBlipForRadius(kothZone.x, kothZone.y, kothZone.z, kothZone.radius)
  SetBlipColour(kothZone.blip, kothBlipColors.neutral)
  SetBlipAlpha(kothZone.blip, 128)

  -- Create center blip
  kothZone.centerBlip = AddBlipForCoord(kothZone.x, kothZone.y, kothZone.z)
  SetBlipSprite(kothZone.centerBlip, 437) -- King crown icon
  SetBlipColour(kothZone.centerBlip, kothBlipColors.neutral)
  SetBlipScale(kothZone.centerBlip, 1.5) -- Larger than team blips
  SetBlipAsShortRange(kothZone.centerBlip, false) -- Always visible

  -- Set blip name
  BeginTextCommandSetBlipName("STRING")
  AddTextComponentSubstringPlayerName("KOTH - Quarry (Neutral)")
  EndTextCommandSetBlipName(kothZone.centerBlip)

  print(('[KOTH] Created KOTH zone blip at %.2f, %.2f'):format(kothZone.x, kothZone.y))
end

-- Create map blips for team zones
local function createTeamBlips()
  print('[KOTH] Creating team zone blips...')

  for team, spawn in pairs(teamSpawns) do
    local blip = AddBlipForRadius(spawn.x, spawn.y, spawn.z, safeZoneRadius)

    -- Set blip properties
    SetBlipColour(blip, blipColors[team] or 0)
    SetBlipAlpha(blip, 128) -- Semi-transparent

    -- Create center blip for the team
    local centerBlip = AddBlipForCoord(spawn.x, spawn.y, spawn.z)
    SetBlipSprite(centerBlip, 84) -- Shield icon
    SetBlipColour(centerBlip, blipColors[team] or 0)
    SetBlipScale(centerBlip, 1.0)
    SetBlipAsShortRange(centerBlip, true)

    -- Set blip name
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentSubstringPlayerName(team:upper() .. " Team Safe Zone")
    EndTextCommandSetBlipName(centerBlip)

    -- Store blips for cleanup
    teamBlips[team] = {
      radius = blip,
      center = centerBlip
    }

    print(('[KOTH] Created %s team blip at %.2f, %.2f'):format(team, spawn.x, spawn.y))
  end
  
  -- Create KOTH zone blip
  createKothBlip()
end

-- Initialize blips when resource starts
AddEventHandler('onClientResourceStart', function(res)
  if GetCurrentResourceName() ~= res then return end
  
  Citizen.SetTimeout(1000, function()
    createTeamBlips()
  end)
end)

-- Update KOTH zone blip color and name
local function updateKothBlip()
  if not DoesBlipExist(kothZone.blip) or not DoesBlipExist(kothZone.centerBlip) then
    return
  end

  local team = kothZone.controllingTeam or 'neutral'
  local color = kothBlipColors[team] or kothBlipColors.neutral

  -- Update colors
  SetBlipColour(kothZone.blip, color)
  SetBlipColour(kothZone.centerBlip, color)

  -- Update name
  BeginTextCommandSetBlipName("STRING")
  if team == 'neutral' then
    AddTextComponentSubstringPlayerName("KOTH - Quarry (Neutral)")
  else
    AddTextComponentSubstringPlayerName(("KOTH - Quarry (%s)"):format(team:upper()))
  end
  EndTextCommandSetBlipName(kothZone.centerBlip)
end

-- KOTH zone tracking thread
Citizen.CreateThread(function()
  while true do
    if playerTeam then
      local playerPed = PlayerPedId()
      local playerCoords = GetEntityCoords(playerPed)
      local distance = #(playerCoords - vector3(kothZone.x, kothZone.y, kothZone.z))

      local wasInZone = inKothZone
      inKothZone = distance <= kothZone.radius

      -- Player entered zone
      if inKothZone and not wasInZone then
        print('[KOTH] Player entered KOTH zone')
        TriggerServerEvent('koth:playerEnteredZone', playerTeam)

        -- Show zone notification
        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName("Entered KOTH Zone - Capture to earn points!")
        EndTextCommandThefeedPostTicker(false, true)
      end

      -- Player left zone
      if not inKothZone and wasInZone then
        print('[KOTH] Player left KOTH zone')
        TriggerServerEvent('koth:playerLeftZone', playerTeam)

        -- Show zone notification
        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName("Left KOTH Zone")
        EndTextCommandThefeedPostTicker(false, true)
      end

      -- Draw zone marker when nearby
      if distance < kothZone.radius + 50.0 then
        local zoneColor = kothZoneColors[kothZone.controllingTeam or 'neutral']
        DrawMarker(1, kothZone.x, kothZone.y, kothZone.z - 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
          kothZone.radius * 2.0, kothZone.radius * 2.0, 50.0,
          zoneColor.r, zoneColor.g, zoneColor.b, zoneColor.a,
          false, false, 2, false, nil, nil, false)
      end
    end

    Citizen.Wait(100)
  end
end)

-- Update zone status from server
RegisterNetEvent('koth:updateZoneStatus', function(status)
  kothZone.controllingTeam = status.controllingTeam
  kothZone.captureProgress = status.captureProgress
  kothZone.playersInZone = status.playersInZone

  -- Update blip color
  updateKothBlip()

  -- Update HUD with zone status
  if inKothZone then
    SendNUIMessage({
      action = 'updateKothZone',
      zoneData = {
        controlling = status.controllingTeam,
        progress = status.captureProgress,
        threshold = status.captureThreshold,
        dominant = status.dominantTeam,
        contested = status.isContested
      }
    })
  else
    SendNUIMessage({ action = 'hideKothZone' })
  end
end)

-- Zone control changed event
RegisterNetEvent('koth:zoneControlChanged', function(newTeam)
  print(('[KOTH] Zone control changed to: %s'):format(newTeam or 'neutral'))
  kothZone.controllingTeam = newTeam
  updateKothBlip()
end)

-- Update team counts for HUD
RegisterNetEvent('koth:updateTeamCounts', function(counts)
  SendNUIMessage({
    action = 'updateTeamCounts',
    counts = counts
  })
end)

-- Update zone points for HUD
RegisterNetEvent('koth:updateZonePoints', function(points)
  SendNUIMessage({
    action = 'updateZonePoints',
    points = points
  })
end)

-- PVP SYSTEM
RegisterNetEvent('koth:enablePVP', function()
  print('[KOTH] Enabling PVP')
  local playerPed = PlayerPedId()
  SetCanAttackFriendly(playerPed, true, false)
  NetworkSetFriendlyFireOption(true)
end)

-- Kill detection system
local isDead = false
local deathTime = 0
local killerServerId = nil

Citizen.CreateThread(function()
  while true do
    local playerPed = PlayerPedId()

    if IsEntityDead(playerPed) and not isDead then
      isDead = true
      deathTime = GetGameTimer()

      -- Get killer info
      local killerPed = GetPedSourceOfDeath(playerPed)
      killerServerId = nil

      if killerPed and killerPed ~= playerPed and IsPedAPlayer(killerPed) then
        killerServerId = GetPlayerServerId(NetworkGetPlayerIndexFromPed(killerPed))
      end

      -- Check if death was in KOTH zone
      local playerCoords = GetEntityCoords(playerPed)
      local distanceToKoth = #(playerCoords - vector3(kothZone.x, kothZone.y, kothZone.z))
      local inZone = distanceToKoth <= kothZone.radius

      -- Report kill to server
      if killerServerId then
        print(('[KOTH] Player killed by %d, in zone: %s'):format(killerServerId, inZone and 'YES' or 'NO'))
        TriggerServerEvent('koth:playerKilled', killerServerId, GetPlayerServerId(PlayerId()), inZone)
      end

      -- Show death screen
      SendNUIMessage({
        action = 'showDeathScreen',
        killer = killerServerId and GetPlayerName(GetPlayerFromServerId(killerServerId)) or 'Unknown',
        killerId = killerServerId or 0
      })
    elseif not IsEntityDead(playerPed) and isDead then
      isDead = false
      SendNUIMessage({ action = 'hideDeathScreen' })
    end

    -- Handle respawn
    if isDead and IsControlPressed(0, 38) then -- E key
      local holdTime = GetGameTimer() - deathTime
      if holdTime > 3000 then -- 3 seconds hold
        -- Respawn at team base
        if playerTeam and teamSpawns[playerTeam] then
          local spawn = teamSpawns[playerTeam]
          NetworkResurrectLocalPlayer(spawn.x, spawn.y, spawn.z, spawn.heading, true, false)
          ClearPedTasksImmediately(playerPed)
          isDead = false
          SendNUIMessage({ action = 'hideDeathScreen' })
        end
      end
    end

    Citizen.Wait(0)
  end
end)

-- Show kill reward popup
RegisterNetEvent('koth:showKillReward', function(data)
  SendNUIMessage({
    action = 'showKillReward',
    xp = data.xp,
    money = data.money,
    inZone = data.inZone,
    victim = data.victimName
  })
end)

-- Show level up popup
RegisterNetEvent('koth:levelUp', function(data)
  SendNUIMessage({
    action = 'showLevelUp',
    oldLevel = data.oldLevel,
    newLevel = data.newLevel
  })
  
  -- Play level up sound
  PlaySoundFrontend(-1, "RANK_UP", "HUD_AWARDS", true)
end)

-- Health bar update
Citizen.CreateThread(function()
  while true do
    local playerPed = PlayerPedId()
    local health = GetEntityHealth(playerPed)
    local maxHealth = GetEntityMaxHealth(playerPed)
    local healthPercent = (health / maxHealth) * 100

    SendNUIMessage({
      action = 'updateHealth',
      health = math.floor(healthPercent)
    })

    Citizen.Wait(250)
  end
end)

-- Initialize HUD when resource starts
AddEventHandler('onClientResourceStart', function(res)
  if GetCurrentResourceName() ~= res then return end
  
  -- Request initial player data
  Citizen.SetTimeout(1000, function()
    TriggerServerEvent('koth:requestPlayerData')
  end)
end)

-- Debug command to manually show team selection
RegisterCommand('showteamselect', function()
  print('[KOTH] Manually showing team selection UI')
  SendNUIMessage({ 
    action = 'showTeamSelect', 
    counts = { red = 0, blue = 0, green = 0 }
  })
  SetNuiFocus(true, true)
end, false)

print('[KOTH] Client loaded successfully')
