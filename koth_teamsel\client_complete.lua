print('[KOTH] Client loading...')

-- TEAM SPAWN COORDS
local teamSpawns = {
  red   = { x=2238.15, y=3788.91, z=35.89, heading=120.5 },
  blue  = { x=1323.77, y=3143.33, z=40.41, heading=282.48 },
  green = { x=1865.99, y=2607.15, z=45.67, heading=276.45 },
}

-- MONEY AND XP SYSTEM - Initialize early
local playerStats = {
  money = 0,  -- Start at 0, will be loaded from server
  xp = 0,
  level = 1,
  kills = 0,
  deaths = 0,
  zone_kills = 0,
  loaded = false  -- Track if data has been loaded
}

-- Function to ensure player data is loaded (defined early so it can be used throughout)
local function ensurePlayerDataLoaded(callback)
  if playerStats.loaded then
    callback()
    return
  end
  
  -- Request data and wait
  print('[KOTH] Requesting player data before shop open...')
  TriggerServerEvent('koth:requestPlayerData')
  
  -- Wait up to 2 seconds for data
  local attempts = 0
  Citizen.CreateThread(function()
    while not playerStats.loaded and attempts < 20 do
      Citizen.Wait(100)
      attempts = attempts + 1
    end
    
    if not playerStats.loaded then
      print('[KOTH] Warning: Player data not loaded after 2 seconds')
      -- Use default values if data doesn't load
      playerStats.money = playerStats.money or 0
    end
    
    callback()
  end)
end

-- Request team counts and show UI
AddEventHandler('playerSpawned', function()
  Citizen.SetTimeout(2000, function()
    print('[KOTH] Requesting team counts...')
    TriggerServerEvent('koth:requestCounts')
  end)
end)

-- Receive counts and show team select
RegisterNetEvent('koth:updateCounts', function(counts)
  print('[KOTH] Received counts, showing UI')
  if counts then
    SendNUIMessage({ action='showTeamSelect', counts=counts })
    SetNuiFocus(true, true)
  end
end)

-- Team selection callback
RegisterNUICallback('selectTeam', function(data, cb)
  print('[KOTH] Team selected:', data.team or 'none')
  if data and data.team then
    TriggerServerEvent('koth:pickTeam', data.team)
    SendNUIMessage({ action='hideAll' })
    SetNuiFocus(false, false)
  end
  cb('ok')
end)

-- Vehicle menu - triggered by ped interaction
RegisterNetEvent('koth:openVehicleMenu', function(items)
  print('[KOTH] Opening vehicle menu')

  -- Use provided items or comprehensive vehicle data
  local vehicles = items or {
    { name = 'Blista', cost = 8000, rent = 1200, img = 'images/vehicles/blista.png' },
    { name = 'Futo', cost = 12000, rent = 1800, img = 'images/vehicles/futo.png' },
    { name = 'Sultan', cost = 15000, rent = 2200, img = 'images/vehicles/sultan.png' },
    { name = 'Elegy', cost = 18000, rent = 2700, img = 'images/vehicles/elegy.png' },
    { name = 'Kuruma', cost = 22000, rent = 3300, img = 'images/vehicles/kuruma.png' },
    { name = 'Armored Kuruma', cost = 35000, rent = 5200, img = 'images/vehicles/kuruma_armored.png' },
    { name = 'Insurgent', cost = 45000, rent = 6700, img = 'images/vehicles/insurgent.png' },
    { name = 'Technical', cost = 28000, rent = 4200, img = 'images/vehicles/technical.png' },
    { name = 'Sandking XL', cost = 32000, rent = 4800, img = 'images/vehicles/sandking.png' },
    { name = 'Mesa', cost = 25000, rent = 3700, img = 'images/vehicles/mesa.png' },
    { name = 'Buzzard', cost = 85000, rent = 12700, img = 'images/vehicles/buzzard.png' },
    { name = 'Savage', cost = 120000, rent = 18000, img = 'images/vehicles/savage.png' },
    { name = 'Rhino Tank', cost = 150000, rent = 22500, img = 'images/vehicles/rhino.png' },
    { name = 'Hydra', cost = 200000, rent = 30000, img = 'images/vehicles/hydra.png' }
  }

  -- Request fresh data from server
  TriggerServerEvent('koth:getMoneyForVehicleShop', vehicles)
  
  -- Set focus immediately to prevent UI issues
  SetNuiFocus(true, true)
end)

-- New event to handle vehicle shop with fresh money data
RegisterNetEvent('koth:showVehicleShopWithMoney', function(data)
  print(('[KOTH] Received vehicle shop data with fresh money: $%d'):format(data.money))
  
  SendNUIMessage({
    action='showMenu',
    type='vehicles',
    items=data.vehicles,
    money = data.money
  })
end)

-- Class menu - triggered by ped interaction
RegisterNetEvent('koth:openClassMenu', function(items)
  print('[KOTH] Opening class menu')

  -- Use provided items or exact classes from the image
  local classes = items or {
    {
      id = 'assault',
      name = 'Assault',
      unlock = 'Unlocked',
      img = 'images/classes/assault.png',
      requiredLevel = 1
    },
    {
      id = 'medic',
      name = 'Medic',
      unlock = 'Unlock at level 5',
      img = 'images/classes/medic.png',
      requiredLevel = 5
    },
    {
      id = 'engineer',
      name = 'Engineer',
      unlock = 'Unlock at level 15',
      img = 'images/classes/engineer.png',
      requiredLevel = 15
    },
    {
      id = 'heavy',
      name = 'Heavy',
      unlock = 'Unlock at level 25',
      img = 'images/classes/heavy.png',
      requiredLevel = 25
    },
    {
      id = 'scout',
      name = 'Scout',
      unlock = 'Unlock at level 40',
      img = 'images/classes/scout.png',
      requiredLevel = 40
    }
  }

  -- Request fresh data from server
  TriggerServerEvent('koth:getDataForClassShop', classes)
  
  -- Set focus immediately to prevent UI issues
  SetNuiFocus(true, true)
end)

-- New event to handle class shop with fresh data
RegisterNetEvent('koth:showClassShopWithData', function(data)
  print(('[KOTH] Received class shop data - Level: %d, Money: $%d'):format(data.level, data.money))
  
  local classes = data.classes
  
  -- Add locked status based on player level
  for i, class in ipairs(classes) do
    class.locked = data.level < class.requiredLevel
    if class.locked then
      print(('[KOTH] Class %s is locked (requires level %d, player is %d)'):format(class.name, class.requiredLevel, data.level))
    end
  end
  
  SendNUIMessage({
    action='showMenu',
    type='classes',
    items=classes,
    playerLevel = data.level,
    money = data.money
  })
end)

-- Vehicle purchase callbacks
RegisterNUICallback('buyVehicle', function(data, cb)
  print('[KOTH] Buying vehicle:', data.name or 'none')
  if data and data.name then
    TriggerServerEvent('koth:buyVehicle', data.name)
  end
  SendNUIMessage({ action='hideAll' })
  SetNuiFocus(false, false)
  cb('ok')
end)

RegisterNUICallback('rentVehicle', function(data, cb)
  print('[KOTH] Renting vehicle:', data.name or 'none')
  if data and data.name then
    TriggerServerEvent('koth:rentVehicle', data.name)
  end
  SendNUIMessage({ action='hideAll' })
  SetNuiFocus(false, false)
  cb('ok')
end)

-- Class selection callback - now shows weapon selection
RegisterNUICallback('selectClass', function(data, cb)
  print('[KOTH] Class selected:', data.id or 'none')
  if data and data.id then
    -- Check if player has required level for this class
    local playerLevel = playerStats and playerStats.level or 1
    local requiredLevels = {
      assault = 1,
      medic = 5,
      engineer = 15,
      heavy = 25,
      scout = 40
    }

    local requiredLevel = requiredLevels[data.id] or 1
    if playerLevel < requiredLevel then
      -- Show error message
      BeginTextCommandThefeedPost("STRING")
      AddTextComponentSubstringPlayerName(('Class locked! Requires level %d (you are level %d)'):format(requiredLevel, playerLevel))
      EndTextCommandThefeedPostTicker(false, true)
      cb('ok')
      return
    end
    -- Define weapons exactly as shown in the image with prices
    local allWeapons = {
      { weapon = 'WEAPON_STONE_HATCHET', name = 'Stone Hatchet', price = 150, img = 'images/guns/stone_hatchet.png' },
      { weapon = 'WEAPON_CROWBAR', name = 'Crowbar', price = 200, img = 'images/guns/crowbar.png' },
      { weapon = 'WEAPON_HATCHET', name = 'Hatchet', price = 250, img = 'images/guns/hatchet.png' },
      { weapon = 'WEAPON_PISTOL', name = 'Pistol', price = 500, img = 'images/guns/pistol.png' },
      { weapon = 'WEAPON_MICROSMG', name = 'Micro SMG', price = 800, img = 'images/guns/micro_smg.png' },
      { weapon = 'WEAPON_COMBATPDW', name = 'Combat PDW', price = 1200, img = 'images/guns/combat_pdw.png' },
      { weapon = 'WEAPON_ASSAULTSMG', name = 'Assault SMG', price = 1500, img = 'images/guns/assault_smg.png' },
      { weapon = 'WEAPON_BULLPUPRIFLE', name = 'Bullpup Rifle', price = 2500, img = 'images/guns/bullpup_rifle.png' },
      { weapon = 'WEAPON_HEAVYRIFLE', name = 'Heavy Rifle', price = 3000, img = 'images/guns/heavy_rifle.png' },
      { weapon = 'WEAPON_ASSAULTRIFLE', name = 'Assault Rifle', price = 3500, img = 'images/guns/assault_rifle.png' },
      { weapon = 'WEAPON_SPECIALCARBINE', name = 'Special Carbine', price = 4000, img = 'images/guns/special_carbine.png' },
      { weapon = 'WEAPON_SPECIALCARBINE_MK2', name = 'Special Carbine Mk2', price = 5000, img = 'images/guns/special_carbine_mk2.png' },
      { weapon = 'WEAPON_CARBINERIFLE_MK2', name = 'Carbine Rifle Mk2', price = 5500, img = 'images/guns/carbine_rifle_mk2.png' }
    }

    -- Request fresh money data directly from server for weapon shop
    print('[KOTH] Requesting fresh money data for weapon shop...')
    TriggerServerEvent('koth:getMoneyForWeaponShop', data.id, allWeapons)
    
    -- Don't set any timeout here - let the server response handle it
  end
  cb('ok')
end)

-- Weapon selection callback
RegisterNUICallback('selectWeapon', function(data, cb)
  print('[KOTH] Weapon selected:', data.weapon or 'none', 'for class:', data.class or 'none', 'price:', data.price or 'none')
  if data and data.weapon and data.class then
    TriggerServerEvent('koth:selectLoadout', data.class, data.weapon, data.price)
  end
  SendNUIMessage({ action='hideAll' })
  SetNuiFocus(false, false)
  cb('ok')
end)

-- WEAPON HOTBAR SYSTEM
local weaponSlots = {
  [1] = nil,
  [2] = nil,
  [3] = nil,
  [4] = nil,
  [5] = nil
}
local currentSlot = 1
local hotbarEnabled = false

-- Weapon icon mapping
local weaponIcons = {
  ['WEAPON_UNARMED'] = 'unarmed',
  ['WEAPON_STONE_HATCHET'] = 'stone_hatchet',
  ['WEAPON_CROWBAR'] = 'crowbar',
  ['WEAPON_HATCHET'] = 'hatchet',
  ['WEAPON_PISTOL'] = 'pistol',
  ['WEAPON_MICROSMG'] = 'micro_smg',
  ['WEAPON_COMBATPDW'] = 'combat_pdw',
  ['WEAPON_ASSAULTSMG'] = 'assault_smg',
  ['WEAPON_BULLPUPRIFLE'] = 'bullpup_rifle',
  ['WEAPON_HEAVYRIFLE'] = 'heavy_rifle',
  ['WEAPON_ASSAULTRIFLE'] = 'assault_rifle',
  ['WEAPON_SPECIALCARBINE'] = 'special_carbine',
  ['WEAPON_SPECIALCARBINE_MK2'] = 'special_carbine_mk2',
  ['WEAPON_CARBINERIFLE_MK2'] = 'carbine_rifle_mk2'
}

-- Function to update hotbar UI
local function updateHotbarUI()
  local slots = {}
  for i = 1, 5 do
    if weaponSlots[i] then
      local ammo = GetAmmoInPedWeapon(PlayerPedId(), GetHashKey(weaponSlots[i]))
      slots[i] = {
        weapon = weaponSlots[i],
        iconClass = weaponIcons[weaponSlots[i]] or 'unarmed',
        ammo = ammo > 0 and tostring(ammo) or nil
      }
    end
  end
  
  SendNUIMessage({
    action = 'updateHotbar',
    slots = slots
  })
  
  SendNUIMessage({
    action = 'setActiveSlot',
    slot = currentSlot
  })
end

-- Function to add weapon to hotbar
local function addWeaponToHotbar(weapon)
  -- Check if weapon already exists in hotbar
  for i = 1, 5 do
    if weaponSlots[i] == weapon then
      return -- Already in hotbar
    end
  end
  
  -- Find first empty slot
  local emptySlot = nil
  for i = 1, 5 do
    if not weaponSlots[i] then
      emptySlot = i
      break
    end
  end
  
  -- If no empty slot, replace the last slot
  if not emptySlot then
    emptySlot = 5
  end
  
  weaponSlots[emptySlot] = weapon
  updateHotbarUI()
end

-- Function to switch weapon
local function switchToSlot(slot)
  if slot < 1 or slot > 5 then return end
  
  currentSlot = slot
  local weapon = weaponSlots[slot]
  
  if weapon then
    local playerPed = PlayerPedId()
    SetCurrentPedWeapon(playerPed, GetHashKey(weapon), true)
  else
    -- Switch to unarmed
    local playerPed = PlayerPedId()
    SetCurrentPedWeapon(playerPed, GetHashKey('WEAPON_UNARMED'), true)
  end
  
  updateHotbarUI()
end

-- Receive weapon from server and give to player
RegisterNetEvent('koth:giveWeapon', function(weapon, classId, price)
  print(('[KOTH] Receiving weapon: %s for class: %s (price: $%s)'):format(weapon or 'none', classId or 'none', price or 'free'))

  if weapon then
    local playerPed = PlayerPedId()

    -- Give the selected weapon with ammo (don't remove all weapons anymore)
    GiveWeaponToPed(playerPed, GetHashKey(weapon), 250, false, true)

    -- Add to hotbar
    addWeaponToHotbar(weapon)
    
    -- Enable hotbar if not already enabled
    if not hotbarEnabled then
      hotbarEnabled = true
      SendNUIMessage({ action = 'showHotbar' })
    end

    -- Set as current weapon
    SetCurrentPedWeapon(playerPed, GetHashKey(weapon), true)

    print(('[KOTH] Successfully equipped %s'):format(weapon))

    -- Show notification with price
    BeginTextCommandThefeedPost("STRING")
    local weaponName = weapon:gsub('WEAPON_', ''):gsub('_', ' ')
    local message = ('Purchased %s for $%s'):format(weaponName, price or '0')
    AddTextComponentSubstringPlayerName(message)
    EndTextCommandThefeedPostTicker(false, true)
  end
end)

-- NUI Callback for weapon slot selection
RegisterNUICallback('selectWeaponSlot', function(data, cb)
  if data.slot then
    switchToSlot(data.slot)
  end
  cb('ok')
end)

-- Disable weapon wheel and handle number keys
Citizen.CreateThread(function()
  while true do
    -- Always disable weapon wheel, not just when hotbar is enabled
    BlockWeaponWheelThisFrame()
    DisableControlAction(0, 37, true) -- TAB key (weapon wheel)
    DisableControlAction(1, 37, true) -- TAB key (weapon wheel) - disable in all control groups
    DisableControlAction(2, 37, true) -- TAB key (weapon wheel)
    
    if hotbarEnabled then
      -- Handle number keys for weapon switching
      for i = 1, 5 do
        if IsControlJustPressed(0, 157 + i - 1) then -- Number keys 1-5
          switchToSlot(i)
        end
      end
      
      -- Update ammo counts periodically
      if GetGameTimer() % 1000 < 50 then -- Every second
        updateHotbarUI()
      end
    end
    
    Citizen.Wait(0)
  end
end)

-- Initialize hotbar when player spawns with a team
RegisterNetEvent('koth:initializeHotbar', function()
  hotbarEnabled = true
  -- Don't add unarmed by default - start with empty slots
  weaponSlots = {
    [1] = nil,
    [2] = nil,
    [3] = nil,
    [4] = nil,
    [5] = nil
  }
  currentSlot = 1
  updateHotbarUI()
  SendNUIMessage({ action = 'showHotbar' })
end)

-- Purchase result handler
RegisterNetEvent('koth:purchaseResult', function(success, message)
  print(('[KOTH] Purchase result: %s - %s'):format(success and 'SUCCESS' or 'FAILED', message))

  -- Show notification
  BeginTextCommandThefeedPost("STRING")
  AddTextComponentSubstringPlayerName(message)
  EndTextCommandThefeedPostTicker(false, true)

  -- If purchase failed, keep the shop open
  if not success then
    -- You could add specific UI feedback here
    print('[KOTH] Purchase failed, keeping shop open')
  end
end)

-- Close menu callback
RegisterNUICallback('closeMenu', function(data, cb)
  print('[KOTH] Menu closed')
  SendNUIMessage({ action='hideAll' })
  SetNuiFocus(false, false)
  cb('ok')
end)

-- Vehicle spawning handler
RegisterNetEvent('koth:spawnVehicle', function(vehicleName, purchaseType, price)
  print(('[KOTH] Spawning vehicle: %s (%s for $%s)'):format(vehicleName or 'none', purchaseType or 'unknown', price or '0'))

  if vehicleName then
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local playerHeading = GetEntityHeading(playerPed)

    -- Vehicle name mapping (simplified)
    local vehicleModels = {
      ['Blista'] = 'blista',
      ['Futo'] = 'futo',
      ['Sultan'] = 'sultan',
      ['Elegy'] = 'elegy2',
      ['Kuruma'] = 'kuruma',
      ['Armored Kuruma'] = 'kuruma2',
      ['Insurgent'] = 'insurgent',
      ['Technical'] = 'technical',
      ['Sandking XL'] = 'sandking',
      ['Mesa'] = 'mesa',
      ['Buzzard'] = 'buzzard2',
      ['Savage'] = 'savage',
      ['Rhino Tank'] = 'rhino',
      ['Hydra'] = 'hydra'
    }

    local modelName = vehicleModels[vehicleName] or vehicleName:lower()
    local modelHash = GetHashKey(modelName)

    -- Request model
    RequestModel(modelHash)
    local timeout = GetGameTimer() + 5000
    while not HasModelLoaded(modelHash) and GetGameTimer() < timeout do
      Citizen.Wait(1)
    end

    if HasModelLoaded(modelHash) then
      -- Find spawn position (in front of player)
      local spawnX = playerCoords.x + math.cos(math.rad(playerHeading)) * 5.0
      local spawnY = playerCoords.y + math.sin(math.rad(playerHeading)) * 5.0

      -- Get proper ground Z coordinate to prevent falling through map
      local foundGround, groundZ = GetGroundZFor_3dCoord(spawnX, spawnY, playerCoords.z + 50.0, false)
      local spawnZ = foundGround and (groundZ + 1.0) or (playerCoords.z + 1.0)

      -- Create vehicle
      local vehicle = CreateVehicle(modelHash, spawnX, spawnY, spawnZ, playerHeading, true, false)

      if DoesEntityExist(vehicle) then
        -- Ensure vehicle is properly placed on ground
        SetEntityCoords(vehicle, spawnX, spawnY, spawnZ, false, false, false, true)
        SetVehicleOnGroundProperly(vehicle)

        -- Set player as owner
        SetVehicleHasBeenOwnedByPlayer(vehicle, true)
        SetEntityAsMissionEntity(vehicle, true, true)

        -- Ensure vehicle has proper collision with ground
        SetEntityCollision(vehicle, true, true)

        -- Wait a moment for physics to settle
        Citizen.Wait(100)

        -- Put player in vehicle
        TaskWarpPedIntoVehicle(playerPed, vehicle, -1)

        print(('[KOTH] Successfully spawned %s at ground level'):format(vehicleName))

        -- Show notification
        BeginTextCommandThefeedPost("STRING")
        local message = ('%s %s for $%s'):format(purchaseType == 'rent' and 'Rented' or 'Purchased', vehicleName, price or '0')
        AddTextComponentSubstringPlayerName(message)
        EndTextCommandThefeedPostTicker(false, true)
      else
        print(('[KOTH] Failed to create vehicle: %s'):format(vehicleName))
      end

      SetModelAsNoLongerNeeded(modelHash)
    else
      print(('[KOTH] Failed to load model: %s'):format(modelName))
    end
  end
end)

-- PED SPAWNING SYSTEM
local spawnedPeds = {}

-- Ped configurations for each team
local pedSpawns = {
  red = {
    { model = `s_m_y_marine_01`,   offset = vector3( 3.0,  2.0, 0.0), text = 'Click [E] To Open Classes Menu',  event = 'koth:openClassMenu'  },
    { model = `s_m_m_autoshop_02`, offset = vector3(-3.0, -2.0, 0.0), text = 'Click [E] To Open Vehicle Menu', event = 'koth:openVehicleMenu' },
  },
  blue = {
    { model = `s_m_y_marine_01`,   offset = vector3( 3.0,  2.0, 0.0), text = 'Click [E] To Open Classes Menu',  event = 'koth:openClassMenu'  },
    { model = `s_m_m_autoshop_02`, offset = vector3(-3.0, -2.0, 0.0), text = 'Click [E] To Open Vehicle Menu', event = 'koth:openVehicleMenu' },
  },
  green = {
    { model = `s_m_y_marine_01`,   offset = vector3( 3.0,  2.0, 0.0), text = 'Click [E] To Open Classes Menu',  event = 'koth:openClassMenu'  },
    { model = `s_m_m_autoshop_02`, offset = vector3(-3.0, -2.0, 0.0), text = 'Click [E] To Open Vehicle Menu', event = 'koth:openVehicleMenu' },
  },
}

-- 3D text helper function
local function Draw3DText(x, y, z, text)
  local onScreen, _x, _y = World3dToScreen2d(x, y, z + 1.0)
  if onScreen then
    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextCentre(true)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    AddTextComponentString(text)
    DrawText(_x, _y)
    local factor = (#text) / 370
    DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 0, 0, 0, 120)
  end
end

-- Spawn peds when resource starts
AddEventHandler('onClientResourceStart', function(res)
  if GetCurrentResourceName() ~= res then return end

  Citizen.CreateThread(function()
    print('[KOTH] Starting ped spawning...')
    Citizen.Wait(3000) -- Wait for everything to load

    for team, list in pairs(pedSpawns) do
      local sv = teamSpawns[team]
      if not sv then
        print('[KOTH] WARNING: No spawn data for team ' .. team)
      else
        local basePos = vector3(sv.x, sv.y, sv.z)
        print(('[KOTH] Spawning peds for team %s at %.2f, %.2f, %.2f'):format(team, basePos.x, basePos.y, basePos.z))

        for _, info in ipairs(list) do
          -- Validate model
          if not (IsModelValid(info.model) and IsModelInCdimage(info.model)) then
            print(('[KOTH] SKIP: Invalid model %s for team %s'):format(info.model, team))
          else
            -- Load model
            RequestModel(info.model)
            local timeout = GetGameTimer() + 5000
            while not HasModelLoaded(info.model) and GetGameTimer() < timeout do
              Citizen.Wait(1)
            end

            if not HasModelLoaded(info.model) then
              print(('[KOTH] FAIL: Could not load model %s'):format(info.model))
            else
              -- Find ground level
              local spawnX, spawnY = basePos.x + info.offset.x, basePos.y + info.offset.y
              local foundGround, groundZ = GetGroundZFor_3dCoord(spawnX, spawnY, basePos.z + 50.0, false)
              local finalZ = foundGround and groundZ or basePos.z

              -- Spawn ped
              local ped = CreatePed(4, info.model, spawnX, spawnY, finalZ, sv.heading or 0.0, false, true)

              if DoesEntityExist(ped) then
                SetEntityHeading(ped, sv.heading or 0.0)
                FreezeEntityPosition(ped, true)
                SetEntityInvincible(ped, true)
                SetBlockingOfNonTemporaryEvents(ped, true)

                table.insert(spawnedPeds, {
                  ped = ped,
                  text = info.text,
                  event = info.event
                })

                print(('[KOTH] SUCCESS: Spawned %s ped for team %s'):format(info.model, team))
              else
                print(('[KOTH] FAIL: CreatePed failed for %s'):format(info.model))
              end

              SetModelAsNoLongerNeeded(info.model)
            end
          end
        end
      end
    end

    print(('[KOTH] Ped spawning complete. Total peds: %d'):format(#spawnedPeds))
  end)
end)

-- Interaction loop for peds
Citizen.CreateThread(function()
  while true do
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)

    for _, info in ipairs(spawnedPeds) do
      if DoesEntityExist(info.ped) then
        local pedCoords = GetEntityCoords(info.ped)
        local distance = #(playerCoords - pedCoords)

        if distance < 10.0 then
          Draw3DText(pedCoords.x, pedCoords.y, pedCoords.z, info.text)

          if distance < 1.5 and IsControlJustReleased(0, 38) then -- E key
            print(('[KOTH] Player interacted with ped: %s'):format(info.event))
            TriggerEvent(info.event)
          end
        end
      end
    end

    Citizen.Wait(0)
  end
end)

-- SAFE ZONE SYSTEM
local playerTeam = nil
local inSafeZone = false
local safeZoneRadius = 25.0 -- Radius of safe zones
local teamBlips = {} -- Store map blips

-- KOTH ZONE SYSTEM
local kothZone = {
  x = 2842.4216,
  y = 2864.8088,
  z = 62.5975,
  radius = 150.0, -- Massive zone radius
  controllingTeam = nil, -- 'red', 'blue', 'green', or nil for neutral
  captureProgress = 0.0, -- 0.0 to 100.0
  captureRate = 1.0, -- Points per second when capturing
  playersInZone = {}, -- Track players in zone by team
  blip = nil, -- Map blip for the zone
  centerBlip = nil -- Center marker blip
}

local inKothZone = false
local kothZoneColors = {
  neutral = { r = 128, g = 128, b = 128, a = 50 }, -- Grey
  red = { r = 255, g = 0, b = 0, a = 50 },
  blue = { r = 0, g = 100, b = 255, a = 50 },
  green = { r = 0, g = 255, b = 0, a = 50 }
}

local kothBlipColors = {
  neutral = 8, -- Grey
  red = 1,     -- Red
  blue = 3,    -- Blue
  green = 2    -- Green
}

-- Zone colors for each team
local zoneColors = {
  red = { r = 255, g = 0, b = 0, a = 50 },
  blue = { r = 0, g = 100, b = 255, a = 50 },
  green = { r = 0, g = 255, b = 0, a = 50 }
}

-- Map blip colors (GTA blip color IDs)
local blipColors = {
  red = 1,    -- Red
  blue = 3,   -- Blue
  green = 2   -- Green
}

-- Create map blips for team zones
local function createTeamBlips()
  print('[KOTH] Creating team zone blips...')

  for team, spawn in pairs(teamSpawns) do
    local blip = AddBlipForRadius(spawn.x, spawn.y, spawn.z, safeZoneRadius)

    -- Set blip properties
    SetBlipColour(blip, blipColors[team] or 0)
    SetBlipAlpha(blip, 128) -- Semi-transparent

    -- Create center blip for the team
    local centerBlip = AddBlipForCoord(spawn.x, spawn.y, spawn.z)
    SetBlipSprite(centerBlip, 84) -- Shield icon
    SetBlipColour(centerBlip, blipColors[team] or 0)
    SetBlipScale(centerBlip, 1.0)
    SetBlipAsShortRange(centerBlip, true)

    -- Set blip name
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentSubstringPlayerName(team:upper() .. " Team Safe Zone")
    EndTextCommandSetBlipName(centerBlip)

    -- Store blips for cleanup
    teamBlips[team] = {
      radius = blip,
      center = centerBlip
    }

    print(('[KOTH] Created %s team blip at %.2f, %.2f'):format(team, spawn.x, spawn.y))
  end
end

-- Create KOTH zone blip
local function createKothBlip()
  print('[KOTH] Creating KOTH zone blip...')

  -- Create radius blip
  kothZone.blip = AddBlipForRadius(kothZone.x, kothZone.y, kothZone.z, kothZone.radius)
  SetBlipColour(kothZone.blip, kothBlipColors.neutral)
  SetBlipAlpha(kothZone.blip, 128)

  -- Create center blip
  kothZone.centerBlip = AddBlipForCoord(kothZone.x, kothZone.y, kothZone.z)
  SetBlipSprite(kothZone.centerBlip, 437) -- King crown icon
  SetBlipColour(kothZone.centerBlip, kothBlipColors.neutral)
  SetBlipScale(kothZone.centerBlip, 1.5) -- Larger than team blips
  SetBlipAsShortRange(kothZone.centerBlip, false) -- Always visible

  -- Set blip name
  BeginTextCommandSetBlipName("STRING")
  AddTextComponentSubstringPlayerName("KOTH - Quarry (Neutral)")
  EndTextCommandSetBlipName(kothZone.centerBlip)

  print(('[KOTH] Created KOTH zone blip at %.2f, %.2f'):format(kothZone.x, kothZone.y))
end

-- Update KOTH zone blip color and name
local function updateKothBlip()
  if not DoesBlipExist(kothZone.blip) or not DoesBlipExist(kothZone.centerBlip) then
    return
  end

  local team = kothZone.controllingTeam or 'neutral'
  local color = kothBlipColors[team] or kothBlipColors.neutral

  -- Update colors
  SetBlipColour(kothZone.blip, color)
  SetBlipColour(kothZone.centerBlip, color)

  -- Update name
  BeginTextCommandSetBlipName("STRING")
  if team == 'neutral' then
    AddTextComponentSubstringPlayerName("KOTH - Quarry (Neutral)")
  else
    AddTextComponentSubstringPlayerName("KOTH - Quarry (" .. team:upper() .. " Controlled)")
  end
  EndTextCommandSetBlipName(kothZone.centerBlip)
end

-- Create blips when resource starts
AddEventHandler('onClientResourceStart', function(res)
  if GetCurrentResourceName() ~= res then return end

  -- Wait a bit then create blips
  Citizen.SetTimeout(1000, function()
    createTeamBlips()
    createKothBlip()
  end)
end)

-- Clean up blips when resource stops
AddEventHandler('onClientResourceStop', function(res)
  if GetCurrentResourceName() ~= res then return end

  print('[KOTH] Cleaning up blips...')

  -- Clean up team blips
  for team, blips in pairs(teamBlips) do
    if DoesBlipExist(blips.radius) then
      RemoveBlip(blips.radius)
    end
    if DoesBlipExist(blips.center) then
      RemoveBlip(blips.center)
    end
  end
  teamBlips = {}

  -- Clean up KOTH blips
  if DoesBlipExist(kothZone.blip) then
    RemoveBlip(kothZone.blip)
  end
  if DoesBlipExist(kothZone.centerBlip) then
    RemoveBlip(kothZone.centerBlip)
  end
end)

-- Store player's team when they spawn - FIXED VERSION
RegisterNetEvent('koth:spawnPlayer', function(spawn)
  if not spawn or not spawn.x then
    print('[KOTH] ERROR: invalid spawn data from server!')
    return
  end

  -- Determine which team this spawn belongs to
  for team, coords in pairs(teamSpawns) do
    if math.abs(spawn.x - coords.x) < 1.0 and math.abs(spawn.y - coords.y) < 1.0 then
      playerTeam = team
      print('[KOTH] Player assigned to team:', team)
      break
    end
  end

  print(('[KOTH] Spawning player at %.2f, %.2f, %.2f'):format(spawn.x, spawn.y, spawn.z))

  local ped = PlayerPedId()

  -- Hide UI and remove focus
  SendNUIMessage({ action = 'hideAll' })
  SetNuiFocus(false, false)

  -- Force spawn the player properly
  DoScreenFadeOut(500)
  Citizen.Wait(500)
  
  -- Respawn the player to ensure they're not dead
  NetworkResurrectLocalPlayer(spawn.x, spawn.y, spawn.z + 1.0, spawn.heading or 0.0, true, false)
  
  -- Get the new ped after respawn
  ped = PlayerPedId()
  
  -- Set position again to be sure
  SetEntityCoords(ped, spawn.x, spawn.y, spawn.z + 1.0, false, false, false, true)
  SetEntityHeading(ped, spawn.heading or 0.0)
  
  -- Clear any tasks and ensure player can move
  ClearPedTasksImmediately(ped)
  SetEntityVisible(ped, true, false)
  FreezeEntityPosition(ped, false)
  SetEntityInvincible(ped, false)
  SetEntityCollision(ped, true, true)
  
  -- Give basic weapon
  GiveWeaponToPed(ped, GetHashKey('WEAPON_PISTOL'), 100, false, true)
  
  Citizen.Wait(500)
  DoScreenFadeIn(500)

  -- Initialize weapon hotbar
  TriggerEvent('koth:initializeHotbar')

  -- Request player data after spawn
  Citizen.SetTimeout(1000, function()
    TriggerServerEvent('koth:requestPlayerData')
  end)

  print('[KOTH] Player spawned successfully')
end)

-- Safe zone monitoring thread
Citizen.CreateThread(function()
  while true do
    if playerTeam then
      local playerPed = PlayerPedId()
      local playerCoords = GetEntityCoords(playerPed)
      local teamSpawn = teamSpawns[playerTeam]

      if teamSpawn then
        local spawnCoords = vector3(teamSpawn.x, teamSpawn.y, teamSpawn.z)
        local distance = #(playerCoords - spawnCoords)
        local shouldBeInSafeZone = distance <= safeZoneRadius

        -- Check if safe zone status changed
        if shouldBeInSafeZone and not inSafeZone then
          -- Entering safe zone
          inSafeZone = true
          print('[KOTH] Entered safe zone')

          -- Enable invincibility
          SetEntityInvincible(playerPed, true)

          -- Show notification
          BeginTextCommandThefeedPost("STRING")
          AddTextComponentSubstringPlayerName("~g~Entered Safe Zone - You are protected")
          EndTextCommandThefeedPostTicker(false, true)

        elseif not shouldBeInSafeZone and inSafeZone then
          -- Leaving safe zone
          inSafeZone = false
          print('[KOTH] Left safe zone')

          -- Disable invincibility
          SetEntityInvincible(playerPed, false)

          -- Show notification
          BeginTextCommandThefeedPost("STRING")
          AddTextComponentSubstringPlayerName("~r~Left Safe Zone - You are vulnerable")
          EndTextCommandThefeedPostTicker(false, true)
        end
      end
    end

    Citizen.Wait(500) -- Check every 500ms
  end
end)

-- KOTH zone monitoring and capture logic
Citizen.CreateThread(function()
  while true do
    if playerTeam then
      local playerPed = PlayerPedId()
      local playerCoords = GetEntityCoords(playerPed)
      local kothCoords = vector3(kothZone.x, kothZone.y, kothZone.z)
      local distance = #(playerCoords - kothCoords)
      local shouldBeInKothZone = distance <= kothZone.radius
      local shouldShowHud = distance <= (kothZone.radius * 1.5) -- Show HUD when near zone

      -- Check if KOTH zone status changed
      if shouldBeInKothZone and not inKothZone then
        -- Entering KOTH zone
        inKothZone = true
        print('[KOTH] Entered KOTH zone')
        TriggerServerEvent('koth:playerEnteredZone', playerTeam)

        -- Show notification
        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName("~y~Entered KOTH Zone - Fight for control!")
        EndTextCommandThefeedPostTicker(false, true)

      elseif not shouldBeInKothZone and inKothZone then
        -- Leaving KOTH zone
        inKothZone = false
        print('[KOTH] Left KOTH zone')
        TriggerServerEvent('koth:playerLeftZone', playerTeam)

        -- Show notification
        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName("~o~Left KOTH Zone")
        EndTextCommandThefeedPostTicker(false, true)
      end

      -- Show/hide KOTH zone status based on proximity
      if shouldShowHud then
        SendNUIMessage({
          action = 'showKothZone',
          status = {
            controllingTeam = kothZone.controllingTeam,
            captureProgress = kothZone.captureProgress,
            playersInZone = kothZone.playersInZone,
            dominantTeam = kothZone.dominantTeam,
            isContested = kothZone.isContested
          }
        })
      else
        SendNUIMessage({ action = 'hideKothZone' })
      end
    end

    Citizen.Wait(1000) -- Check every second
  end
end)

-- Draw safe zone circles
Citizen.CreateThread(function()
  while true do
    if playerTeam then
      for team, spawn in pairs(teamSpawns) do
        local color = zoneColors[team]
        if color then
          -- Draw colored circle on ground
          DrawMarker(
            1, -- Circle marker
            spawn.x, spawn.y, spawn.z - 1.0, -- Position
            0.0, 0.0, 0.0, -- Direction
            0.0, 0.0, 0.0, -- Rotation
            safeZoneRadius * 2.0, safeZoneRadius * 2.0, 2.0, -- Scale
            color.r, color.g, color.b, color.a, -- Color
            false, true, 2, false, nil, nil, false -- Options
          )

          -- Draw border circle
          DrawMarker(
            25, -- Ring marker
            spawn.x, spawn.y, spawn.z + 1.0, -- Position
            0.0, 0.0, 0.0, -- Direction
            0.0, 0.0, 0.0, -- Rotation
            safeZoneRadius * 2.0, safeZoneRadius * 2.0, 2.0, -- Scale
            color.r, color.g, color.b, 150, -- Color (more opaque border)
            false, true, 2, false, nil, nil, false -- Options
          )
        end
      end
    end

    Citizen.Wait(0)
  end
end)

-- Draw KOTH zone circle
Citizen.CreateThread(function()
  while true do
    local playerCoords = GetEntityCoords(PlayerPedId())
    local kothCoords = vector3(kothZone.x, kothZone.y, kothZone.z)
    local distance = #(playerCoords - kothCoords)

    -- Only draw if player is reasonably close (performance optimization)
    if distance < kothZone.radius * 2.0 then
      local team = kothZone.controllingTeam or 'neutral'
      local color = kothZoneColors[team]

      if color then
        -- Draw massive colored circle on ground
        DrawMarker(
          1, -- Circle marker
          kothZone.x, kothZone.y, kothZone.z - 2.0, -- Position (lower for visibility)
          0.0, 0.0, 0.0, -- Direction
          0.0, 0.0, 0.0, -- Rotation
          kothZone.radius * 2.0, kothZone.radius * 2.0, 4.0, -- Scale (massive)
          color.r, color.g, color.b, color.a, -- Color
          false, true, 2, false, nil, nil, false -- Options
        )

        -- Draw border ring (more visible)
        DrawMarker(
          25, -- Ring marker
          kothZone.x, kothZone.y, kothZone.z + 2.0, -- Position
          0.0, 0.0, 0.0, -- Direction
          0.0, 0.0, 0.0, -- Rotation
          kothZone.radius * 2.0, kothZone.radius * 2.0, 4.0, -- Scale
          color.r, color.g, color.b, 200, -- Color (more opaque border)
          false, true, 2, false, nil, nil, false -- Options
        )

        -- Draw center marker for the zone
        if distance < kothZone.radius then
          DrawMarker(
            29, -- Crown marker
            kothZone.x, kothZone.y, kothZone.z + 10.0, -- Position (elevated)
            0.0, 0.0, 0.0, -- Direction
            0.0, 0.0, 0.0, -- Rotation
            5.0, 5.0, 5.0, -- Scale
            color.r, color.g, color.b, 255, -- Color (fully opaque)
            true, true, 2, false, nil, nil, false -- Options (bob up and down)
          )
        end
      end
    end

    Citizen.Wait(0)
  end
end)

-- Team count updates from server
RegisterNetEvent('koth:updateTeamCounts', function(teamCounts)
  if teamCounts then
    print(('[KOTH] Team counts updated - Red: %d, Blue: %d, Green: %d'):format(
      teamCounts.red or 0, teamCounts.blue or 0, teamCounts.green or 0
    ))

    -- Update HUD with new team counts
    UpdateTeamCounts(teamCounts)
  end
end)

-- Zone points updates from server
RegisterNetEvent('koth:updateZonePoints', function(zonePoints)
  if zonePoints then
    print(('[KOTH] Zone points updated - Red: %d, Green: %d, Blue: %d'):format(
      zonePoints.red or 0, zonePoints.green or 0, zonePoints.blue or 0
    ))

    -- Update HUD with new zone points
    UpdateZonePoints(zonePoints)
  end
end)

-- KOTH zone status updates from server
RegisterNetEvent('koth:zoneControlChanged', function(controllingTeam)
  print(('[KOTH] Zone control changed to: %s'):format(controllingTeam or 'neutral'))

  -- Update local zone data
  kothZone.controllingTeam = controllingTeam

  -- Update map blip
  updateKothBlip()

  -- Show notification
  BeginTextCommandThefeedPost("STRING")
  if controllingTeam then
    AddTextComponentSubstringPlayerName(('~y~KOTH Zone captured by %s team!'):format(controllingTeam:upper()))
  else
    AddTextComponentSubstringPlayerName('~w~KOTH Zone is now neutral')
  end
  EndTextCommandThefeedPostTicker(false, true)
end)

RegisterNetEvent('koth:updateZoneStatus', function(status)
  if not status then return end

  -- Update local zone data
  kothZone.controllingTeam = status.controllingTeam
  kothZone.captureProgress = status.captureProgress
  kothZone.playersInZone = status.playersInZone or {}
  kothZone.dominantTeam = status.dominantTeam
  kothZone.isContested = status.isContested

  -- Update map blip if control changed
  updateKothBlip()

  -- Update KOTH zone status in HUD
  SendNUIMessage({
    action = 'updateKothStatus',
    status = status
  })
end)

-- PERMANENT HUD SYSTEM
local lastHealth = 100
local lastMoney = 0

-- Initialize permanent HUD when player spawns
AddEventHandler('playerSpawned', function()
  Citizen.SetTimeout(3000, function() -- Wait for everything to load
    print('[KOTH] Initializing permanent HUD')

    -- Initialize HUD with minimal data - real data will come from server
    SendNUIMessage({
      action = 'initHUD',
      playerData = {
        playerName = GetPlayerName(PlayerId()),
        playerMoney = 0, -- Will be updated when server sends real data
        playerLevel = 1, -- Will be updated when server sends real data
        playerXP = 0,
        playerMaxXP = 100,
        playerHealth = GetEntityHealth(PlayerPedId()) - 100, -- GTA health is 100-200, convert to 0-100
        teamCounts = { red = 0, green = 0, blue = 0 }, -- Will be updated by server
        zonePoints = { red = 0, green = 0, blue = 0 } -- Zone control points
      }
    })

    -- Request real player data from server
    TriggerServerEvent('koth:requestPlayerData')
  end)
end)

-- Health monitoring thread
Citizen.CreateThread(function()
  while true do
    local playerPed = PlayerPedId()
    local currentHealth = GetEntityHealth(playerPed) - 100 -- Convert GTA health (100-200) to 0-100
    currentHealth = math.max(0, math.min(100, currentHealth)) -- Clamp between 0-100

    -- Only update if health changed
    if math.abs(currentHealth - lastHealth) > 1 then
      lastHealth = currentHealth
      SendNUIMessage({
        action = 'updateHealth',
        health = currentHealth
      })
    end

    Citizen.Wait(100) -- Check health every 100ms for smooth updates
  end
end)

-- Disable default GTA health/armor HUD
Citizen.CreateThread(function()
  while true do
    -- Hide default health and armor bars
    HideHudComponentThisFrame(3)  -- SP_HEALTH
    HideHudComponentThisFrame(4)  -- SP_ARMOUR

    Citizen.Wait(0)
  end
end)

-- Money update function (call this when player money changes)
function UpdatePlayerMoney(newMoney)
  lastMoney = newMoney
  SendNUIMessage({
    action = 'updateMoney',
    money = newMoney
  })
end

-- Team counts update function (call this when team player counts change)
function UpdateTeamCounts(teamCounts)
  SendNUIMessage({
    action = 'updateTeamCounts',
    teamCounts = teamCounts
  })
end

-- Zone points update function (call this when zone points change)
function UpdateZonePoints(zonePoints)
  SendNUIMessage({
    action = 'updateZonePoints',
    zonePoints = zonePoints
  })
end

-- DEATH SYSTEM
local isDead = false
local deathPosition = nil

-- Kill tracking
local lastKiller = nil
local killProcessed = false

-- Death detection thread
Citizen.CreateThread(function()
  while true do
    local playerPed = PlayerPedId()
    local currentHealth = GetEntityHealth(playerPed)

    -- Check if player just died
    if currentHealth <= 100 and not isDead then -- GTA health 100 = dead
      print('[KOTH DEBUG] Death detected! Health dropped to ' .. currentHealth)
      isDead = true
      deathPosition = GetEntityCoords(playerPed)
      killProcessed = false

      print('[KOTH] Player died, handling death')

      -- Get killer information
      local killer = GetPedSourceOfDeath(playerPed)
      local killerData = nil
      local killerServerId = nil

      if killer and killer ~= 0 and killer ~= playerPed then
        -- Check if killer is a player
        local killerPlayer = NetworkGetPlayerIndexFromPed(killer)
        if killerPlayer and killerPlayer ~= -1 then
          killerServerId = GetPlayerServerId(killerPlayer)
          local killerName = GetPlayerName(killerPlayer)

          killerData = {
            id = killerServerId,
            name = killerName
          }

          print(('[KOTH] Killed by player: [%s] %s'):format(killerServerId, killerName))

          -- Check if kill happened in KOTH zone
          local inZone = false
          if kothZone and kothZone.active then
            local distance = #(deathPosition - vector3(kothZone.x, kothZone.y, kothZone.z))
            inZone = distance <= kothZone.radius
          end

          -- Send kill event to server for reward processing
          if not killProcessed then
            print('[KOTH DEBUG] About to send kill event to server...')
            TriggerServerEvent('koth:playerKilled', killerServerId, GetPlayerServerId(PlayerId()), inZone)
            killProcessed = true
            print(('[KOTH] Kill event sent - Killer: %d | Victim: %d | In Zone: %s'):format(killerServerId, GetPlayerServerId(PlayerId()), tostring(inZone)))
          else
            print('[KOTH DEBUG] Kill already processed, skipping...')
          end
        end
      end

      -- If no killer found, show unknown
      if not killerData then
        killerData = {
          id = '000000',
          name = 'Unknown'
        }
      end

      -- Immediately set health to prevent auto-respawn
      SetEntityHealth(playerPed, 100)

      -- Make player lay down with death animation
      RequestAnimDict("dead")
      while not HasAnimDictLoaded("dead") do
        Citizen.Wait(1)
      end

      -- Play death animation (laying on ground)
      TaskPlayAnim(playerPed, "dead", "dead_a", 8.0, -8.0, -1, 1, 0, false, false, false)

      -- Freeze player in death position
      Citizen.SetTimeout(1000, function() -- Wait for animation to start
        FreezeEntityPosition(playerPed, true)
      end)

      -- Make player invincible but keep collision for visibility
      SetEntityInvincible(playerPed, true)

      -- Show death screen
      SendNUIMessage({
        action = 'showDeathScreen',
        killer = killerData
      })

      print('[KOTH] Player frozen and death screen shown')
    end

    Citizen.Wait(100)
  end
end)

-- Death state management thread (separate from detection)
Citizen.CreateThread(function()
  while true do
    if isDead then
      local playerPed = PlayerPedId()

      -- Continuously disable all controls
      DisableAllControlActions(0)
      DisableAllControlActions(1)
      DisableAllControlActions(2)

      -- Keep player laying down
      if not IsEntityPlayingAnim(playerPed, "dead", "dead_a", 3) then
        TaskPlayAnim(playerPed, "dead", "dead_a", 8.0, -8.0, -1, 1, 0, false, false, false)
      end

      -- Maintain death state
      SetEntityInvincible(playerPed, true)
      SetEntityHealth(playerPed, 100)

      -- Prevent screen fade
      if IsScreenFadedOut() then
        DoScreenFadeIn(500)
      end

      -- Disable automatic respawn
      if IsPlayerDead(PlayerId()) then
        NetworkResurrectLocalPlayer(deathPosition.x, deathPosition.y, deathPosition.z, 0.0, false, false)
        SetEntityHealth(PlayerPedId(), 100) -- Keep at minimum health
      end
    end

    Citizen.Wait(0) -- Run every frame when dead
  end
end)

-- Key detection for death screen (since controls are disabled)
local eKeyPressed = false

Citizen.CreateThread(function()
  while true do
    if isDead then
      -- Check for E key press (even when controls are disabled)
      if IsDisabledControlPressed(0, 38) then -- E key
        if not eKeyPressed then
          eKeyPressed = true
          SendNUIMessage({
            action = 'keyDown',
            key = 'E'
          })
        end
      else
        if eKeyPressed then
          eKeyPressed = false
          SendNUIMessage({
            action = 'keyUp',
            key = 'E'
          })
        end
      end
    else
      -- Reset key state when not dead
      if eKeyPressed then
        eKeyPressed = false
      end
    end

    Citizen.Wait(0) -- Check every frame when dead
  end
end)

-- Disable automatic respawn completely
Citizen.CreateThread(function()
  while true do
    -- Disable automatic respawn
    SetFadeInAfterDeathArrest(false)
    SetFadeOutAfterDeath(false)

    Citizen.Wait(0)
  end
end)

-- Handle respawn requests from UI
RegisterNUICallback('playerRespawn', function(data, cb)
  local respawnType = data.type or 'manual'
  print(('[KOTH] Respawn requested: %s'):format(respawnType))

  if isDead and playerTeam then
    -- Get team spawn location
    local spawn = teamSpawns[playerTeam]
    if spawn then
      local playerPed = PlayerPedId()

      -- Reset death state first
      isDead = false

      -- Stop death animation
      ClearPedTasks(playerPed)

      -- Re-enable everything
      FreezeEntityPosition(playerPed, false)
      SetEntityInvincible(playerPed, false)
      SetEntityCollision(playerPed, true, true)
      SetPedCanRagdoll(playerPed, true)
      SetPedCanRagdollFromPlayerImpact(playerPed, true)

      -- Teleport to team spawn
      SetEntityCoords(playerPed, spawn.x, spawn.y, spawn.z + 1.0, false, false, false, true)
      SetEntityHeading(playerPed, spawn.heading or 0.0)

      -- Heal player after teleport
      SetEntityHealth(playerPed, 200) -- Full health
      ClearPedBloodDamage(playerPed)

      -- Hide death screen
      SendNUIMessage({ action = 'hideDeathScreen' })

      print(('[KOTH] Player respawned at %s team spawn'):format(playerTeam))

      -- Show notification
      BeginTextCommandThefeedPost("STRING")
      AddTextComponentSubstringPlayerName(('~g~Respawned at %s team base'):format(playerTeam:upper()))
      EndTextCommandThefeedPostTicker(false, true)
    else
      print('[KOTH] ERROR: No spawn location for team ' .. playerTeam)
    end
  end

  cb('ok')
end)

-- PVP SYSTEM
Citizen.CreateThread(function()
  while true do
    -- Enable PVP for all players
    SetCanAttackFriendly(PlayerPedId(), true, true)
    NetworkSetFriendlyFireOption(true)

    -- Allow damage to all players
    for _, playerId in ipairs(GetActivePlayers()) do
