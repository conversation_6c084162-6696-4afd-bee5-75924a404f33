# KOTH Team Selection System - Complete Fix Guide

## Issues Fixed

1. **Safe Zones** - Added safe zone system where <PERSON><PERSON> is disabled in team spawn areas (25m radius)
2. **UI Menu Delays** - Fixed by requesting fresh data from server before opening shops
3. **Player HUD** - Now shows actual database stats instead of hardcoded values
4. **Team Persistence** - Removed team persistence between sessions (players pick team each time)

## Installation Instructions

### 1. Backup your current files first!

### 2. Replace the following files:

- **Client Script**: Replace `client.lua` with `client_final_complete.lua`
- **Server Script**: Keep using `server_fixed.lua` 
- **JavaScript**: Replace `script.js` with `script_fixed.js`
- **FXManifest**: Replace `fxmanifest.lua` with `fxmanifest_fixed.lua`

### 3. Rename the files:

```bash
# In koth_teamsel folder:
mv fxmanifest.lua fxmanifest_backup.lua
mv fxmanifest_fixed.lua fxmanifest.lua
```

### 4. Ensure your server.cfg has:

```
ensure oxmysql
ensure koth_teamsel
ensure koth_admin
```

## Key Features Working

### Safe Zones
- Each team spawn has a 25m radius safe zone
- PVP is automatically disabled when entering any safe zone
- PVP is re-enabled when leaving safe zones
- Visual markers show safe zone boundaries
- Notifications appear when entering/leaving safe zones

### UI Improvements
- Vehicle and weapon shops now load player money from database
- No more 10-second delays when opening menus
- Class selection checks player level from database
- All menus show real-time player stats

### HUD System
- Shows actual player name from database
- Displays real money amount (updates after purchases)
- Shows correct player level and XP progress
- Updates team counts in real-time
- Health bar changes color based on health percentage

### Database Integration
- Player stats (money, XP, level, kills, deaths) are saved
- Team selection is NOT saved (session-only)
- All purchases deduct from database money
- Kill rewards are added to database
- Level progression is tracked

## Testing Checklist

1. **Team Selection**
   - [ ] Team selection UI appears on spawn
   - [ ] Can select any team regardless of previous session
   - [ ] Spawns at correct team location

2. **Safe Zones**
   - [ ] Cannot damage players in any team's safe zone
   - [ ] Can damage players outside safe zones
   - [ ] Notifications appear when entering/leaving

3. **Shop Peds**
   - [ ] Vehicle shop opens without delay
   - [ ] Weapon shop opens without delay
   - [ ] Shows correct player money from database
   - [ ] Purchases deduct money properly

4. **HUD**
   - [ ] Shows correct player name
   - [ ] Shows actual money amount
   - [ ] Updates after purchases/kills
   - [ ] Shows correct level and XP

5. **KOTH Zone**
   - [ ] Can capture zone with team
   - [ ] Zone changes color when captured
   - [ ] Points awarded to controlling team

## Troubleshooting

### If menus still have delays:
- Check server console for database connection errors
- Ensure oxmysql is running before koth_teamsel

### If safe zones don't work:
- Check that `zoneColors` is defined in client script
- Verify team spawn coordinates are correct

### If HUD shows wrong stats:
- Use `/checkstats` command to verify database values
- Check server console for player data loading errors

### If team persists between sessions:
- Make sure you're using the fixed server script
- Check that database doesn't have a `current_team` column

## Console Commands

- `/checkstats` - Shows your current stats from database
- `/serverstats` - (server console) Shows all player data

## Support

If you encounter any issues:
1. Check server console for errors
2. Check F8 console for client errors
3. Verify all files were replaced correctly
4. Ensure database is properly connected

## Credits

Fixed by: Assistant
Original system by: You
