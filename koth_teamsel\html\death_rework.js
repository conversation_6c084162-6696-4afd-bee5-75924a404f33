// JavaScript for reworked death screen UI with hold E progress and NUI callback

let holdStart = null;
let holdDuration = 5000; // 5 seconds
let holdInterval = null;

window.addEventListener('message', function(event) {
    const data = event.data;
    const deathScreen = document.getElementById('death-screen');
    const respawnFill = document.getElementById('respawn-fill');
    const killerName = document.getElementById('killer-name');
    const bleedoutTimer = document.getElementById('bleedout-timer');

    if (data.action === 'showDeathScreen') {
        if (deathScreen) {
            deathScreen.style.display = 'block';
        }
        if (killerName) {
            killerName.textContent = data.killer || 'Unknown';
        }
        if (respawnFill) {
            respawnFill.style.width = '0%';
        }
        if (bleedoutTimer) {
            bleedoutTimer.textContent = '50';
        }
        startHoldCheck();
    } else if (data.action === 'hideDeathScreen') {
        if (deathScreen) {
            deathScreen.style.display = 'none';
        }
        stopHoldCheck();
        resetProgress();
    } else if (data.action === 'updateDeathTimer') {
        if (bleedoutTimer) {
            bleedoutTimer.textContent = data.bleedoutTimer || '0';
        }
    }
});

function startHoldCheck() {
    if (holdInterval) return;
    holdStart = null;
    holdInterval = setInterval(() => {
        if (isKeyPressed('KeyE')) {
            if (!holdStart) holdStart = Date.now();
            let elapsed = Date.now() - holdStart;
            let progress = Math.min(100, (elapsed / holdDuration) * 100);
            updateProgress(progress);
            if (elapsed >= holdDuration) {
                sendRespawn();
                stopHoldCheck();
            }
        } else {
            holdStart = null;
            updateProgress(0);
        }
    }, 50);
}

function stopHoldCheck() {
    if (holdInterval) {
        clearInterval(holdInterval);
        holdInterval = null;
    }
}

function updateProgress(progress) {
    const respawnFill = document.getElementById('respawn-fill');
    if (respawnFill) {
        respawnFill.style.width = progress + '%';
        if (progress >= 75) {
            respawnFill.style.backgroundColor = '#00ff00';
        } else if (progress >= 50) {
            respawnFill.style.backgroundColor = '#ffff00';
        } else {
            respawnFill.style.backgroundColor = '#ff0000';
        }
    }
}

function resetProgress() {
    updateProgress(0);
}

function sendRespawn() {
    fetch(`https://${GetParentResourceName()}/respawn`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
    }).then(resp => resp.json()).then(data => {
        // Respawn acknowledged
    });
}

function isKeyPressed(code) {
    return !!(window.pressedKeys && window.pressedKeys[code]);
}

window.pressedKeys = {};

window.addEventListener('keydown', (e) => {
    window.pressedKeys[e.code] = true;
});

window.addEventListener('keyup', (e) => {
    window.pressedKeys[e.code] = false;
});

console.log('[KOTH DEATH REWORK] Death screen script loaded');
