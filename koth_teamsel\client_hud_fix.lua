-- HUD FIX: Ensure player stats are properly displayed

-- Force update HUD with player data
local function updateHUDDisplay(data)
    if not data then return end
    
    print('[KOTH HUD FIX] Updating HUD with player data')
    
    -- Send the data in the format the JavaScript expects
    SendNUIMessage({
        action = 'updatePlayerData',
        data = {
            player_name = data.player_name or GetPlayerName(PlayerId()),
            money = data.money or 0,
            level = data.level or 1,
            xp = data.xp or 0,
            kills = data.kills or 0,
            deaths = data.deaths or 0
        }
    })
    
    -- Also update individual elements
    SendNUIMessage({
        action = 'updateMoney',
        money = data.money or 0
    })
end

-- Override the updatePlayerData event to ensure HUD updates
RegisterNetEvent('koth:updatePlayerData', function(data)
    print('[KOTH HUD FIX] Intercepting player data update')
    playerStats = data
    
    -- Update HUD multiple times to ensure it catches
    updateHUDDisplay(data)
    
    -- Retry after a short delay
    Citizen.SetTimeout(500, function()
        updateHUDDisplay(data)
    end)
    
    -- And once more after a longer delay
    Citizen.SetTimeout(2000, function()
        updateHUDDisplay(data)
    end)
end)

-- Periodically request player data to keep HUD updated
Citizen.CreateThread(function()
    while true do
        -- Request fresh player data every 30 seconds
        TriggerServerEvent('koth:requestPlayerData')
        Citizen.Wait(30000)
    end
end)

-- Command to manually refresh HUD
RegisterCommand('refreshhud', function()
    print('[KOTH HUD FIX] Manually refreshing HUD')
    TriggerServerEvent('koth:requestPlayerData')
    
    -- If we have cached data, update immediately
    if playerStats then
        updateHUDDisplay(playerStats)
    end
end, false)

-- Initialize HUD on resource start
AddEventHandler('onClientResourceStart', function(res)
    if GetCurrentResourceName() ~= res then return end
    
    print('[KOTH HUD FIX] Resource started, initializing HUD')
    
    -- Request player data multiple times to ensure it loads
    for i = 1, 3 do
        Citizen.SetTimeout(i * 1000, function()
            TriggerServerEvent('koth:requestPlayerData')
        end)
    end
end)

-- Also update HUD when player spawns
RegisterNetEvent('koth:spawnPlayer', function(spawnData)
    print('[KOTH HUD FIX] Player spawned, updating HUD')
    
    -- Request fresh data
    TriggerServerEvent('koth:requestPlayerData')
    
    -- If we have cached data, update immediately
    if playerStats then
        Citizen.SetTimeout(1000, function()
            updateHUDDisplay(playerStats)
        end)
    end
end)

print('[KOTH HUD FIX] Loaded successfully')
