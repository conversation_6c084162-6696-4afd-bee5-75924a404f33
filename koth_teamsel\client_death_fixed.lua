-- DEATH SYSTEM FIXED: Complete rewrite with proper team tracking and no auto-respawn

local isDead = false
local deathTime = 0
local killerServerId = nil
local respawnHoldTime = 0
local respawnProgress = 0
local hasReleasedE = true -- Start as true to require release before hold timer starts
local isHoldingE = false -- Track current E key state
local playerTeam = nil -- Track player's current team
local hasSelectedTeam = false -- Track if player has already selected a team
local isRespawning = false -- Prevent multiple respawn attempts

-- Death animation
local function playDeathAnimation()
    local playerPed = PlayerPedId()
    
    -- Request animation dictionary
    RequestAnimDict("dead")
    while not HasAnimDictLoaded("dead") do
        Citizen.Wait(1)
    end
    
    -- Play dead animation
    TaskPlayAnim(playerPed, "dead", "dead_a", 8.0, -8.0, -1, 1, 0, false, false, false)
    
    -- Keep player in ragdoll
    SetPedToRagdoll(playerPed, 1000, 1000, 0, true, true, false)
end

-- Function to respawn player at team base
local function respawnAtTeamBase()
    if isRespawning then
        return -- Prevent multiple respawn attempts
    end
    
    isRespawning = true
    
    -- Team spawn coordinates
    local teamSpawns = {
        red = { x = 2238.15, y = 3788.91, z = 35.89, heading = 120.5 },
        blue = { x = 1323.77, y = 3143.33, z = 40.41, heading = 282.48 },
        green = { x = 1865.99, y = 2607.15, z = 45.67, heading = 276.45 }
    }
    
    -- Use stored team or default to red
    local team = playerTeam or 'red'
    local spawn = teamSpawns[team]
    
    print(('[KOTH DEATH FIXED] Respawning player at %s team base'):format(team))
    
    -- Hide death screen immediately
    SendNUIMessage({ action = 'hideDeathScreen' })
    
    -- Clear death state FIRST
    isDead = false
    
    -- Use a more reliable respawn method
    Citizen.CreateThread(function()
        -- Respawn at team base
        NetworkResurrectLocalPlayer(spawn.x, spawn.y, spawn.z, spawn.heading, true, false)
        
        -- Wait for respawn to complete
        Citizen.Wait(200)
        
        -- Get the new player ped after respawn
        local playerPed = PlayerPedId()
        
        -- Ensure player is alive and positioned correctly
        if IsEntityDead(playerPed) then
            NetworkResurrectLocalPlayer(spawn.x, spawn.y, spawn.z, spawn.heading, true, false)
            Citizen.Wait(100)
            playerPed = PlayerPedId()
        end
        
        -- Clear all tasks and animations immediately
        ClearPedTasksImmediately(playerPed)
        ClearPedSecondaryTask(playerPed)
        
        -- Ensure proper positioning
        SetEntityCoords(playerPed, spawn.x, spawn.y, spawn.z, false, false, false, true)
        SetEntityHeading(playerPed, spawn.heading)
        
        -- Make absolutely sure player can move
        FreezeEntityPosition(playerPed, false)
        SetEntityInvincible(playerPed, false)
        SetPedCanRagdoll(playerPed, true)
        SetPedCanSwitchWeapon(playerPed, true)
        
        -- Enable collision
        SetEntityCollision(playerPed, true, true)
        
        -- Remove all weapons first
        RemoveAllPedWeapons(playerPed, true)
        
        -- Give basic weapon
        GiveWeaponToPed(playerPed, GetHashKey('WEAPON_PISTOL'), 250, false, true)
        
        -- Additional revive and unfreeze calls to fix freezing
        ClearPedTasksImmediately(playerPed)
        ClearPedSecondaryTask(playerPed)
        SetEntityCoords(playerPed, spawn.x, spawn.y, spawn.z, false, false, false, true)
        SetEntityHeading(playerPed, spawn.heading)
        FreezeEntityPosition(playerPed, false)
        SetEntityInvincible(playerPed, false)
        SetPedCanRagdoll(playerPed, true)
        SetPedCanSwitchWeapon(playerPed, true)
        SetEntityCollision(playerPed, true, true)
        NetworkResurrectLocalPlayer(spawn.x, spawn.y, spawn.z, spawn.heading, true, false)
        Citizen.Wait(100)
        
        -- Show respawn notification
        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName(("Respawned at %s team base"):format(team))
        EndTextCommandThefeedPostTicker(false, true)
        
        -- Multiple checks to ensure movement
        for i = 1, 5 do
            Citizen.Wait(100)
            local ped = PlayerPedId()
            if IsPedFrozen(ped) then
                FreezeEntityPosition(ped, false)
                print('[KOTH DEATH FIXED] Unfreezing player attempt ' .. i)
            end
        end
        
        -- Reset respawn flag
        isRespawning = false
        
        print('[KOTH DEATH FIXED] Respawn completed successfully')
    end)
end

-- Listen for team selection to track player's team
RegisterNetEvent('koth:spawnPlayer', function(spawnData)
    -- Only process if we haven't died (prevents team selection on respawn)
    if not isDead then
        -- Determine team from spawn location
        local teamSpawns = {
            red = { x = 2238.15, y = 3788.91 },
            blue = { x = 1323.77, y = 3143.33 },
            green = { x = 1865.99, y = 2607.15 }
        }
        
        for team, spawn in pairs(teamSpawns) do
            if math.abs(spawnData.x - spawn.x) < 1.0 and math.abs(spawnData.y - spawn.y) < 1.0 then
                playerTeam = team
                hasSelectedTeam = true
                print(('[KOTH DEATH FIXED] Player joined %s team'):format(team))
                break
            end
        end
        
        -- Make sure player is alive when spawning
        local playerPed = PlayerPedId()
        if IsEntityDead(playerPed) then
            NetworkResurrectLocalPlayer(spawnData.x, spawnData.y, spawnData.z, spawnData.heading or 0.0, true, false)
        end
        
        -- Teleport to spawn
        SetEntityCoords(playerPed, spawnData.x, spawnData.y, spawnData.z, false, false, false, true)
        SetEntityHeading(playerPed, spawnData.heading or 0.0)
        
        -- Ensure player can move
        FreezeEntityPosition(playerPed, false)
        ClearPedTasksImmediately(playerPed)
        
        -- Reset death state if spawning
        isDead = false
        SendNUIMessage({ action = 'hideDeathScreen' })
    end
end)

-- Override the default spawn event to prevent team selection on death
AddEventHandler('playerSpawned', function()
    if hasSelectedTeam and isDead then
        -- Prevent default spawn behavior
        exports.spawnmanager:setAutoSpawn(false)
        return
    end
end)

-- Main death detection thread
Citizen.CreateThread(function()
    -- Disable auto-spawn
    exports.spawnmanager:setAutoSpawn(false)
    exports.spawnmanager:forceRespawn()
    
    while true do
        local playerPed = PlayerPedId()
        
        if IsEntityDead(playerPed) and not isDead then
            -- Player just died
            isDead = true
            deathTime = GetGameTimer()
            respawnHoldTime = 0
            respawnProgress = 0
            hasReleasedE = false
            
            print('[KOTH DEATH FIXED] Player died')
            
            -- Prevent auto-respawn
            exports.spawnmanager:setAutoSpawn(false)
            
            -- Play death animation
            playDeathAnimation()
            
            -- Get killer info
            local killerPed = GetPedSourceOfDeath(playerPed)
            killerServerId = nil
            
            if killerPed and killerPed ~= playerPed and IsPedAPlayer(killerPed) then
                killerServerId = GetPlayerServerId(NetworkGetPlayerIndexFromPed(killerPed))
            end
            
            -- Check if death was in KOTH zone
            local playerCoords = GetEntityCoords(playerPed)
            local kothZone = { x = 2842.4216, y = 2864.8088, z = 62.5975, radius = 150.0 }
            local distanceToKoth = #(playerCoords - vector3(kothZone.x, kothZone.y, kothZone.z))
            local inZone = distanceToKoth <= kothZone.radius
            
            -- Report kill to server
            if killerServerId then
                print(('[KOTH DEATH FIXED] Killed by player %d, in zone: %s'):format(killerServerId, inZone and 'YES' or 'NO'))
                TriggerServerEvent('koth:playerKilled', killerServerId, GetPlayerServerId(PlayerId()), inZone)
            end
            
            -- Show death screen
            SendNUIMessage({
                action = 'showDeathScreen',
                killer = killerServerId and GetPlayerName(GetPlayerFromServerId(killerServerId)) or 'Unknown',
                killerId = killerServerId or 0
            })
            
        elseif isDead then
            -- Player is dead, handle respawn
            
            -- Keep playing death animation
            if not IsEntityPlayingAnim(playerPed, "dead", "dead_a", 3) then
                playDeathAnimation()
            end
            
            -- Calculate bleedout timer (50 seconds total)
            local elapsedTime = (GetGameTimer() - deathTime) / 1000
            local bleedoutRemaining = math.max(0, 50 - math.floor(elapsedTime))
            
            -- Update death screen timer
            SendNUIMessage({
                action = 'updateDeathTimer',
                bleedoutTimer = bleedoutRemaining
            })
            
            -- Handle respawn input - MUST HOLD E FOR 5 SECONDS
            local currentlyHoldingE = IsControlPressed(0, 38) -- E key pressed
            
            if currentlyHoldingE then
                -- E key is being held
                if not isHoldingE then
                    -- Just started holding E
                    isHoldingE = true
                    if hasReleasedE then
                        -- Only start timer if E was released since last death
                        respawnHoldTime = 0
                        print('[KOTH DEATH FIXED] Started holding E for respawn')
                    end
                end
                
                -- Continue holding E and we've released it before
                if hasReleasedE and isHoldingE then
                    respawnHoldTime = respawnHoldTime + GetFrameTime()
                    respawnProgress = math.min(100, (respawnHoldTime / 5.0) * 100) -- 5 seconds to respawn
                    
                    -- Update respawn progress bar
                    SendNUIMessage({
                        action = 'updateRespawnProgress',
                        progress = respawnProgress
                    })
                    
                    -- Respawn when held for 5 seconds
                    if respawnHoldTime >= 5.0 and not isRespawning then
                        print('[KOTH DEATH FIXED] 5 seconds completed, respawning...')
                        respawnAtTeamBase()
                        
                        -- Reset all states for next death
                        hasReleasedE = false
                        isHoldingE = false
                        respawnHoldTime = 0
                        respawnProgress = 0
                    end
                end
            else
                -- E key not being held
                if isHoldingE then
                    -- Just released E
                    isHoldingE = false
                    hasReleasedE = true
                    print('[KOTH DEATH FIXED] Released E key')
                    
                    -- Reset progress if not completed
                    if respawnHoldTime < 5.0 then
                        respawnHoldTime = 0
                        respawnProgress = 0
                        
                        -- Reset progress bar
                        SendNUIMessage({
                            action = 'updateRespawnProgress',
                            progress = 0
                        })
                    end
                end
            end
            
            -- Auto-respawn after 50 seconds
            if elapsedTime >= 50 then
                print('[KOTH DEATH FIXED] Auto-respawning after bleedout')
                respawnAtTeamBase()
            end
        end
        
        Citizen.Wait(0)
    end
end)

-- Disable default respawn controls when dead
Citizen.CreateThread(function()
    while true do
        if isDead then
            local playerPed = PlayerPedId()
            
            -- Keep player in position
            if not IsEntityPlayingAnim(playerPed, "dead", "dead_a", 3) then
                FreezeEntityPosition(playerPed, true)
            end
            
            -- Disable ALL default respawn controls
            DisableControlAction(0, 7, true)   -- L
            DisableControlAction(0, 18, true)  -- Enter
            DisableControlAction(0, 22, true)  -- Space
            DisableControlAction(0, 176, true) -- Enter/Return
            DisableControlAction(0, 249, true) -- N
            DisableControlAction(0, 45, true)  -- R
            DisableControlAction(0, 284, true) -- F1
            DisableControlAction(0, 311, true) -- K
            
            -- Disable movement
            DisableControlAction(0, 30, true)  -- A/D
            DisableControlAction(0, 31, true)  -- W/S
            DisableControlAction(0, 32, true)  -- W
            DisableControlAction(0, 33, true)  -- S
            DisableControlAction(0, 34, true)  -- A
            DisableControlAction(0, 35, true)  -- D
            
            -- Disable looking around
            DisableControlAction(0, 1, true)   -- Mouse X
            DisableControlAction(0, 2, true)   -- Mouse Y
            
            -- Disable weapon switching
            DisableControlAction(0, 37, true)  -- Tab
            DisableControlAction(0, 157, true) -- 1
            DisableControlAction(0, 158, true) -- 2
            DisableControlAction(0, 160, true) -- 3
            DisableControlAction(0, 164, true) -- 4
            DisableControlAction(0, 165, true) -- 5
        end
        Citizen.Wait(0)
    end
end)

-- Command to manually set team (for testing)
RegisterCommand('setteam', function(source, args)
    if args[1] and (args[1] == 'red' or args[1] == 'blue' or args[1] == 'green') then
        playerTeam = args[1]
        hasSelectedTeam = true
        print(('[KOTH DEATH FIXED] Manually set team to %s'):format(playerTeam))
    end
end, false)

-- Export function for other scripts to set player team
exports('setPlayerTeam', function(team)
    if team and (team == 'red' or team == 'blue' or team == 'green') then
        playerTeam = team
        hasSelectedTeam = true
        print(('[KOTH DEATH FIXED] Team set to %s via export'):format(team))
    end
end)

print('[KOTH DEATH FIXED] Death system loaded successfully')
