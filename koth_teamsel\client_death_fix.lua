-- DEATH SYSTEM FIX: Proper death handling with animation and team respawn

local isDead = false
local deathTime = 0
local killerServerId = nil
local respawnHoldTime = 0
local respawnProgress = 0
local hasReleasedE = true

-- Death animation
local function playDeathAnimation()
    local playerPed = PlayerPedId()
    
    -- Request animation dictionary
    RequestAnimDict("dead")
    while not HasAnimDictLoaded("dead") do
        Citizen.Wait(1)
    end
    
    -- Play dead animation
    TaskPlayAnim(playerPed, "dead", "dead_a", 8.0, -8.0, -1, 1, 0, false, false, false)
    
    -- Keep player in ragdoll
    SetPedToRagdoll(playerPed, 1000, 1000, 0, true, true, false)
end

-- Override the default death system
Citizen.CreateThread(function()
    while true do
        local playerPed = PlayerPedId()
        
        if IsEntityDead(playerPed) and not isDead then
            -- Player just died
            isDead = true
            deathTime = GetGameTimer()
            respawnHoldTime = 0
            respawnProgress = 0
            hasReleasedE = false
            
            print('[KOTH DEATH FIX] Player died')
            
            -- Disable auto-respawn
            NetworkResurrectLocalPlayer(GetEntityCoords(playerPed), GetEntityHeading(playerPed), false, false)
            
            -- Freeze player and play animation
            FreezeEntityPosition(playerPed, true)
            playDeathAnimation()
            
            -- Get killer info
            local killerPed = GetPedSourceOfDeath(playerPed)
            killerServerId = nil
            
            if killerPed and killerPed ~= playerPed and IsPedAPlayer(killerPed) then
                killerServerId = GetPlayerServerId(NetworkGetPlayerIndexFromPed(killerPed))
            end
            
            -- Check if death was in KOTH zone
            local playerCoords = GetEntityCoords(playerPed)
            local kothZone = { x = 2842.4216, y = 2864.8088, z = 62.5975, radius = 150.0 }
            local distanceToKoth = #(playerCoords - vector3(kothZone.x, kothZone.y, kothZone.z))
            local inZone = distanceToKoth <= kothZone.radius
            
            -- Report kill to server
            if killerServerId then
                print(('[KOTH DEATH FIX] Killed by player %d, in zone: %s'):format(killerServerId, inZone and 'YES' or 'NO'))
                TriggerServerEvent('koth:playerKilled', killerServerId, GetPlayerServerId(PlayerId()), inZone)
            end
            
            -- Show death screen
            SendNUIMessage({
                action = 'showDeathScreen',
                killer = killerServerId and GetPlayerName(GetPlayerFromServerId(killerServerId)) or 'Unknown',
                killerId = killerServerId or 0
            })
            
        elseif isDead then
            -- Player is dead, handle respawn
            local playerPed = PlayerPedId()
            
            -- Keep playing death animation
            if not IsEntityPlayingAnim(playerPed, "dead", "dead_a", 3) then
                playDeathAnimation()
            end
            
            -- Keep player frozen
            FreezeEntityPosition(playerPed, true)
            
            -- Calculate bleedout timer (50 seconds total)
            local elapsedTime = (GetGameTimer() - deathTime) / 1000
            local bleedoutRemaining = math.max(0, 50 - math.floor(elapsedTime))
            
            -- Update death screen timer
            SendNUIMessage({
                action = 'updateDeathTimer',
                bleedoutTimer = bleedoutRemaining
            })
            
            -- Handle respawn input
            if IsControlPressed(0, 38) then -- E key pressed
                if hasReleasedE then -- Only count if they released and pressed again
                    respawnHoldTime = respawnHoldTime + GetFrameTime()
                    respawnProgress = math.min(100, (respawnHoldTime / 3.0) * 100) -- 3 seconds to respawn
                    
                    -- Update respawn progress bar
                    SendNUIMessage({
                        action = 'updateRespawnProgress',
                        progress = respawnProgress
                    })
                    
                    -- Respawn when held for 3 seconds
                    if respawnHoldTime >= 3.0 then
                        print('[KOTH DEATH FIX] Respawning player at team base')
                        
                        -- Get player's team
                        local playerTeam = GetResourceKvpString('playerTeam') or 'red' -- Default to red if no team
                        
                        -- Team spawn coordinates
                        local teamSpawns = {
                            red = { x = 2238.15, y = 3788.91, z = 35.89, heading = 120.5 },
                            blue = { x = 1323.77, y = 3143.33, z = 40.41, heading = 282.48 },
                            green = { x = 1865.99, y = 2607.15, z = 45.67, heading = 276.45 }
                        }
                        
                        local spawn = teamSpawns[playerTeam] or teamSpawns.red
                        
                        -- Respawn at team base
                        NetworkResurrectLocalPlayer(spawn.x, spawn.y, spawn.z, spawn.heading, true, false)
                        
                        -- Clear death state
                        isDead = false
                        ClearPedTasksImmediately(playerPed)
                        FreezeEntityPosition(playerPed, false)
                        
                        -- Give basic weapon
                        GiveWeaponToPed(playerPed, GetHashKey('WEAPON_PISTOL'), 250, false, true)
                        
                        -- Hide death screen
                        SendNUIMessage({ action = 'hideDeathScreen' })
                        
                        -- Show respawn notification
                        BeginTextCommandThefeedPost("STRING")
                        AddTextComponentSubstringPlayerName("Respawned at team base")
                        EndTextCommandThefeedPostTicker(false, true)
                    end
                end
            else
                -- E key released, reset hold time
                hasReleasedE = true
                if respawnHoldTime > 0 then
                    respawnHoldTime = 0
                    respawnProgress = 0
                    
                    -- Reset progress bar
                    SendNUIMessage({
                        action = 'updateRespawnProgress',
                        progress = 0
                    })
                end
            end
            
            -- Auto-respawn after 50 seconds
            if elapsedTime >= 50 then
                print('[KOTH DEATH FIX] Auto-respawning after bleedout')
                
                -- Get player's team
                local playerTeam = GetResourceKvpString('playerTeam') or 'red'
                
                -- Team spawn coordinates
                local teamSpawns = {
                    red = { x = 2238.15, y = 3788.91, z = 35.89, heading = 120.5 },
                    blue = { x = 1323.77, y = 3143.33, z = 40.41, heading = 282.48 },
                    green = { x = 1865.99, y = 2607.15, z = 45.67, heading = 276.45 }
                }
                
                local spawn = teamSpawns[playerTeam] or teamSpawns.red
                
                -- Respawn at team base
                NetworkResurrectLocalPlayer(spawn.x, spawn.y, spawn.z, spawn.heading, true, false)
                
                -- Clear death state
                isDead = false
                ClearPedTasksImmediately(playerPed)
                FreezeEntityPosition(playerPed, false)
                
                -- Give basic weapon
                GiveWeaponToPed(playerPed, GetHashKey('WEAPON_PISTOL'), 250, false, true)
                
                -- Hide death screen
                SendNUIMessage({ action = 'hideDeathScreen' })
            end
            
        elseif not IsEntityDead(playerPed) and not isDead then
            -- Player is alive, ensure they're not frozen
            if IsPedFatallyInjured(playerPed) then
                -- Prevent getting stuck in injured state
                ClearPedTasksImmediately(playerPed)
            end
        end
        
        Citizen.Wait(0)
    end
end)

-- Store player team when selected
RegisterNetEvent('koth:teamSelected', function(team)
    SetResourceKvp('playerTeam', team)
    print('[KOTH DEATH FIX] Stored player team:', team)
end)

-- Override spawn event to store team
RegisterNetEvent('koth:spawnPlayer', function(spawnData)
    -- Extract team from spawn coordinates
    local teamSpawns = {
        red = { x = 2238.15, y = 3788.91, z = 35.89 },
        blue = { x = 1323.77, y = 3143.33, z = 40.41 },
        green = { x = 1865.99, y = 2607.15, z = 45.67 }
    }
    
    for team, spawn in pairs(teamSpawns) do
        if math.abs(spawnData.x - spawn.x) < 1.0 and math.abs(spawnData.y - spawn.y) < 1.0 then
            SetResourceKvp('playerTeam', team)
            print('[KOTH DEATH FIX] Detected and stored team from spawn:', team)
            break
        end
    end
end)

-- Disable default respawn controls
Citizen.CreateThread(function()
    while true do
        if isDead then
            -- Disable all respawn controls except E
            DisableControlAction(0, 7, true)   -- L
            DisableControlAction(0, 18, true)  -- Enter
            DisableControlAction(0, 22, true)  -- Space
            DisableControlAction(0, 176, true) -- Enter/Return
            DisableControlAction(0, 249, true) -- N
            
            -- Disable movement
            DisableControlAction(0, 30, true)  -- A/D
            DisableControlAction(0, 31, true)  -- W/S
            DisableControlAction(0, 32, true)  -- W
            DisableControlAction(0, 33, true)  -- S
            DisableControlAction(0, 34, true)  -- A
            DisableControlAction(0, 35, true)  -- D
            
            -- Disable camera movement
            DisableControlAction(0, 1, true)   -- Mouse look
            DisableControlAction(0, 2, true)   -- Mouse look
        end
        Citizen.Wait(0)
    end
end)

print('[KOTH DEATH FIX] Loaded successfully')
