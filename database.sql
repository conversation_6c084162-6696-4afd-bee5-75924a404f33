-- KOTH Money and XP System Database
-- Import this into your EXISTING phpMyAdmin database on Zap Hosting
-- Run each CREATE TABLE statement separately if you get errors

-- Drop existing tables if they exist (optional - only if you want to start fresh)
-- DROP TABLE IF EXISTS `koth_players`;
-- DROP TABLE IF EXISTS `koth_kill_log`;
-- DROP TABLE IF EXISTS `koth_xp_levels`;
-- DROP TABLE IF EXISTS `koth_money_log`;
-- DROP TABLE IF EXISTS `koth_server_stats`;

-- Player data table
CREATE TABLE `koth_players` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `txid` varchar(50) NOT NULL,
  `steam_id` varchar(100) DEFAULT NULL,
  `discord_id` varchar(100) DEFAULT NULL,
  `player_name` varchar(100) NOT NULL,
  `money` int(11) NOT NULL DEFAULT 1000,
  `xp` int(11) NOT NULL DEFAULT 0,
  `level` int(11) NOT NULL DEFAULT 1,
  `kills` int(11) NOT NULL DEFAULT 0,
  `deaths` int(11) NOT NULL DEFAULT 0,
  `zone_kills` int(11) NOT NULL DEFAULT 0,
  `total_playtime` int(11) NOT NULL DEFAULT 0,
  `current_team` varchar(10) DEFAULT NULL,
  `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_txid` (`txid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Kill log table for tracking kills and rewards
CREATE TABLE `koth_kill_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `killer_txid` varchar(50) NOT NULL,
  `victim_txid` varchar(50) NOT NULL,
  `killer_name` varchar(100) NOT NULL,
  `victim_name` varchar(100) NOT NULL,
  `in_zone` tinyint(1) NOT NULL DEFAULT 0,
  `money_reward` int(11) NOT NULL DEFAULT 50,
  `xp_reward` int(11) NOT NULL DEFAULT 50,
  `weapon_used` varchar(100) DEFAULT NULL,
  `kill_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- XP levels table for level progression
CREATE TABLE `koth_xp_levels` (
  `level` int(11) NOT NULL,
  `xp_required` int(11) NOT NULL,
  `level_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default XP level requirements
INSERT INTO `koth_xp_levels` (`level`, `xp_required`, `level_name`) VALUES
(1, 0, 'Rookie'),
(2, 100, 'Private'),
(3, 250, 'Corporal'),
(4, 500, 'Sergeant'),
(5, 1000, 'Lieutenant'),
(6, 1750, 'Captain'),
(7, 2750, 'Major'),
(8, 4000, 'Colonel'),
(9, 6000, 'General'),
(10, 8500, 'Commander'),
(11, 12000, 'Elite'),
(12, 16000, 'Master'),
(13, 21000, 'Grandmaster'),
(14, 27000, 'Legend'),
(15, 35000, 'Mythic'),
(16, 45000, 'Immortal'),
(17, 57000, 'Divine'),
(18, 72000, 'Ascended'),
(19, 90000, 'Transcendent'),
(20, 112000, 'God Tier');

-- Money transactions log
CREATE TABLE IF NOT EXISTS `koth_money_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `txid` varchar(50) NOT NULL,
  `transaction_type` enum('kill_reward','zone_kill_reward','purchase','admin_give','admin_take') NOT NULL,
  `amount` int(11) NOT NULL,
  `balance_before` int(11) NOT NULL,
  `balance_after` int(11) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `transaction_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `txid` (`txid`),
  KEY `transaction_time` (`transaction_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Server statistics table
CREATE TABLE IF NOT EXISTS `koth_server_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `stat_name` varchar(50) NOT NULL UNIQUE,
  `stat_value` bigint(20) NOT NULL DEFAULT 0,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `stat_name` (`stat_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default server stats
INSERT INTO `koth_server_stats` (`stat_name`, `stat_value`) VALUES
('total_kills', 0),
('total_zone_kills', 0),
('total_money_earned', 0),
('total_xp_earned', 0),
('total_players', 0);

-- Team statistics table
CREATE TABLE IF NOT EXISTS `koth_team_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `team_name` varchar(10) NOT NULL,
  `total_kills` int(11) NOT NULL DEFAULT 0,
  `total_deaths` int(11) NOT NULL DEFAULT 0,
  `zone_captures` int(11) NOT NULL DEFAULT 0,
  `zone_time_held` int(11) NOT NULL DEFAULT 0,
  `current_players` int(11) NOT NULL DEFAULT 0,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `team_name` (`team_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default team stats
INSERT INTO `koth_team_stats` (`team_name`) VALUES
('red'),
('blue'),
('green');

-- Team history table for tracking team changes
CREATE TABLE IF NOT EXISTS `koth_team_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `txid` varchar(50) NOT NULL,
  `team` varchar(10) NOT NULL,
  `joined_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `txid` (`txid`),
  KEY `joined_at` (`joined_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Sample data for testing (optional - remove if you don't want test data)
-- INSERT INTO `koth_players` (`txid`, `player_name`, `team`, `money`, `xp`, `level`, `kills`, `deaths`) VALUES
-- ('license:test123', 'TestPlayer1', 'red', 1500, 150, 2, 3, 1),
-- ('license:test456', 'TestPlayer2', 'blue', 2000, 300, 3, 6, 2);

-- Migration for existing databases (run this if you already have the table without team column)
-- ALTER TABLE `koth_players` ADD COLUMN `team` varchar(10) DEFAULT NULL AFTER `player_name`;
