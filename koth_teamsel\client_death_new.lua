-- NEW DEATH SYSTEM: Robust death handling with hold E to respawn and improved state management

local isDead = false
local deathTime = 0
local respawnHoldTime = 0
local respawnProgress = 0
local playerTeam = nil
local hasSelectedTeam = false
local isRespawning = false
local hasReleasedE = true
local isHoldingE = false

local teamSpawns = {
    red = { x = 2238.15, y = 3788.91, z = 35.89, heading = 120.5 },
    blue = { x = 1323.77, y = 3143.33, z = 40.41, heading = 282.48 },
    green = { x = 1865.99, y = 2607.15, z = 45.67, heading = 276.45 }
}

local function playDeathAnimation()
    local playerPed = PlayerPedId()
    RequestAnimDict("dead")
    while not HasAnimDictLoaded("dead") do
        Citizen.Wait(1)
    end
    TaskPlayAnim(playerPed, "dead", "dead_a", 8.0, -8.0, -1, 1, 0, false, false, false)
    SetPedToRagdoll(playerPed, 1000, 1000, 0, true, true, false)
end

local function respawnAtTeamBase()
    if isRespawning then return end
    isRespawning = true

    local team = playerTeam or 'red'
    local spawn = teamSpawns[team]

    SendNUIMessage({ action = 'hideDeathScreen' })
    isDead = false

    Citizen.CreateThread(function()
        NetworkResurrectLocalPlayer(spawn.x, spawn.y, spawn.z, spawn.heading, true, false)
        Citizen.Wait(200)
        local playerPed = PlayerPedId()
        if IsEntityDead(playerPed) then
            NetworkResurrectLocalPlayer(spawn.x, spawn.y, spawn.z, spawn.heading, true, false)
            Citizen.Wait(100)
            playerPed = PlayerPedId()
        end
        ClearPedTasksImmediately(playerPed)
        ClearPedSecondaryTask(playerPed)
        SetEntityCoords(playerPed, spawn.x, spawn.y, spawn.z, false, false, false, true)
        SetEntityHeading(playerPed, spawn.heading)
        FreezeEntityPosition(playerPed, false)
        SetEntityInvincible(playerPed, false)
        SetPedCanRagdoll(playerPed, true)
        SetPedCanSwitchWeapon(playerPed, true)
        SetEntityCollision(playerPed, true, true)
        RemoveAllPedWeapons(playerPed, true)
        GiveWeaponToPed(playerPed, GetHashKey('WEAPON_PISTOL'), 250, false, true)

        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName(("Respawned at %s team base"):format(team))
        EndTextCommandThefeedPostTicker(false, true)

        for i = 1, 5 do
            Citizen.Wait(100)
            local ped = PlayerPedId()
            if IsPedFrozen(ped) then
                FreezeEntityPosition(ped, false)
            end
        end

        isRespawning = false
    end)
end

RegisterNetEvent('koth:spawnPlayer', function(spawnData)
    if not isDead then
        for team, spawn in pairs(teamSpawns) do
            if math.abs(spawnData.x - spawn.x) < 1.0 and math.abs(spawnData.y - spawn.y) < 1.0 then
                playerTeam = team
                hasSelectedTeam = true
                break
            end
        end
        local playerPed = PlayerPedId()
        if IsEntityDead(playerPed) then
            NetworkResurrectLocalPlayer(spawnData.x, spawnData.y, spawnData.z, spawnData.heading or 0.0, true, false)
        end
        SetEntityCoords(playerPed, spawnData.x, spawnData.y, spawnData.z, false, false, false, true)
        SetEntityHeading(playerPed, spawnData.heading or 0.0)
        FreezeEntityPosition(playerPed, false)
        ClearPedTasksImmediately(playerPed)
        isDead = false
        SendNUIMessage({ action = 'hideDeathScreen' })
    end
end)

AddEventHandler('playerSpawned', function()
    if hasSelectedTeam and isDead then
        exports.spawnmanager:setAutoSpawn(false)
        return
    end
end)

Citizen.CreateThread(function()
    exports.spawnmanager:setAutoSpawn(false)
    exports.spawnmanager:forceRespawn()

    while true do
        local playerPed = PlayerPedId()
        if IsEntityDead(playerPed) and not isDead then
            isDead = true
            deathTime = GetGameTimer()
            respawnHoldTime = 0
            respawnProgress = 0
            hasReleasedE = true
            isHoldingE = false
            playDeathAnimation()
            SendNUIMessage({ action = 'showDeathScreen' })
        elseif isDead then
            if not IsEntityPlayingAnim(playerPed, "dead", "dead_a", 3) then
                playDeathAnimation()
            end
            local elapsedTime = (GetGameTimer() - deathTime) / 1000
            local bleedoutRemaining = math.max(0, 50 - math.floor(elapsedTime))
            SendNUIMessage({ action = 'updateDeathTimer', bleedoutTimer = bleedoutRemaining })

            local currentlyHoldingE = IsControlPressed(0, 38)
            if currentlyHoldingE then
                if not isHoldingE then
                    isHoldingE = true
                    if hasReleasedE then
                        respawnHoldTime = 0
                        print('[KOTH DEATH NEW] Started holding E')
                    end
                end
                if hasReleasedE and isHoldingE then
                    respawnHoldTime = respawnHoldTime + GetFrameTime()
                    respawnProgress = math.min(100, (respawnHoldTime / 5.0) * 100)
                    print(('[KOTH DEATH NEW] Respawn progress: %.2f%%'):format(respawnProgress))
                    SendNUIMessage({ action = 'updateRespawnProgress', progress = respawnProgress })
                    if respawnHoldTime >= 5.0 and not isRespawning then
                        print('[KOTH DEATH NEW] Respawn hold complete, respawning...')
                        respawnAtTeamBase()
                        hasReleasedE = false
                        isHoldingE = false
                        respawnHoldTime = 0
                        respawnProgress = 0
                    end
                end
            else
                if isHoldingE then
                    isHoldingE = false
                    hasReleasedE = true
                    print('[KOTH DEATH NEW] Released E key')
                    if respawnHoldTime < 5.0 then
                        respawnHoldTime = 0
                        respawnProgress = 0
                        SendNUIMessage({ action = 'updateRespawnProgress', progress = 0 })
                    end
                end
            end

            if elapsedTime >= 50 then
                respawnAtTeamBase()
            end
        end
        Citizen.Wait(0)
    end
end)

Citizen.CreateThread(function()
    while true do
        if isDead then
            local playerPed = PlayerPedId()
            if not IsEntityPlayingAnim(playerPed, "dead", "dead_a", 3) then
                FreezeEntityPosition(playerPed, true)
            end
            DisableControlAction(0, 7, true)
            DisableControlAction(0, 18, true)
            DisableControlAction(0, 22, true)
            DisableControlAction(0, 176, true)
            DisableControlAction(0, 249, true)
            DisableControlAction(0, 45, true)
            DisableControlAction(0, 284, true)
            DisableControlAction(0, 311, true)
            DisableControlAction(0, 30, true)
            DisableControlAction(0, 31, true)
            DisableControlAction(0, 32, true)
            DisableControlAction(0, 33, true)
            DisableControlAction(0, 34, true)
            DisableControlAction(0, 35, true)
            DisableControlAction(0, 1, true)
            DisableControlAction(0, 2, true)
            DisableControlAction(0, 37, true)
            DisableControlAction(0, 157, true)
            DisableControlAction(0, 158, true)
            DisableControlAction(0, 160, true)
            DisableControlAction(0, 164, true)
            DisableControlAction(0, 165, true)
        end
        Citizen.Wait(0)
    end
end)
