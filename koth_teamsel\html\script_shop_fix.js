// SHOP UI FIX: Ensure shop menus display properly instead of black screen

// Handle overlay display
function showOverlay() {
    const overlay = document.getElementById('overlay');
    if (overlay) {
        overlay.style.display = 'block';
        overlay.style.visibility = 'visible';
        overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)'; // Semi-transparent instead of black
    }
}

// Handle shop-specific displays
window.addEventListener('message', function(event) {
    const data = event.data;
    
    // Show overlay when requested
    if (data.action === 'showOverlay') {
        console.log('[KOTH SHOP FIX] Showing overlay');
        showOverlay();
    }
    
    // Handle vehicle shop display
    if (data.action === 'showVehicleShop') {
        console.log('[KOTH SHOP FIX] Showing vehicle shop');
        showOverlay();
        
        // Hide all other UI elements first
        document.querySelectorAll('#team-select, #menu-container, #weapons-shop, #classes-selection, #death-screen').forEach(el => {
            if (el) el.style.display = 'none';
        });
        
        // Show vehicles shop
        const vehiclesShop = document.getElementById('vehicles-shop');
        if (vehiclesShop) {
            vehiclesShop.style.display = 'block';
            vehiclesShop.style.visibility = 'visible';
            
            // Update money display
            const moneyElement = document.getElementById('vehicle-player-money');
            if (moneyElement && data.money !== undefined) {
                moneyElement.textContent = data.money.toLocaleString();
            }
            
            // Populate vehicles
            const vehiclesGrid = document.getElementById('vehicles-grid');
            if (vehiclesGrid && data.items) {
                vehiclesGrid.innerHTML = '';
                data.items.forEach(vehicle => {
                    const vehicleCard = createVehicleCard(vehicle, data.money);
                    vehiclesGrid.appendChild(vehicleCard);
                });
            }
        }
    }
    
    // Handle class selection display
    if (data.action === 'showClassSelection') {
        console.log('[KOTH SHOP FIX] Showing class selection');
        showOverlay();
        
        // Hide all other UI elements first
        document.querySelectorAll('#team-select, #menu-container, #weapons-shop, #vehicles-shop, #death-screen').forEach(el => {
            if (el) el.style.display = 'none';
        });
        
        // Show classes selection
        const classesSelection = document.getElementById('classes-selection');
        if (classesSelection) {
            classesSelection.style.display = 'block';
            classesSelection.style.visibility = 'visible';
            
            // Populate classes
            const classesContainer = document.getElementById('classes-container');
            if (classesContainer && data.items) {
                classesContainer.innerHTML = '';
                data.items.forEach(classItem => {
                    const classCard = createClassCard(classItem, data.playerLevel);
                    classesContainer.appendChild(classCard);
                });
            }
        }
    }
    
    // Handle weapon shop display
    if (data.action === 'showWeaponShop' || data.action === 'showWeaponSelect') {
        console.log('[KOTH SHOP FIX] Showing weapon shop');
        showOverlay();
        
        // Hide all other UI elements first
        document.querySelectorAll('#team-select, #menu-container, #vehicles-shop, #classes-selection, #death-screen').forEach(el => {
            if (el) el.style.display = 'none';
        });
        
        // Show weapons shop
        const weaponsShop = document.getElementById('weapons-shop');
        if (weaponsShop) {
            weaponsShop.style.display = 'block';
            weaponsShop.style.visibility = 'visible';
            
            // Update money display
            const moneyElement = document.getElementById('player-money');
            if (moneyElement && data.money !== undefined) {
                moneyElement.textContent = data.money.toLocaleString();
            }
            
            // Populate weapons
            const weaponsGrid = document.getElementById('weapons-grid');
            if (weaponsGrid && data.weapons) {
                weaponsGrid.innerHTML = '';
                data.weapons.forEach(weapon => {
                    const weaponCard = createWeaponCard(weapon, data.money, data.class);
                    weaponsGrid.appendChild(weaponCard);
                });
            }
        }
    }
    
    // Handle the old showMenu action for backwards compatibility
    if (data.action === 'showMenu') {
        console.log('[KOTH SHOP FIX] Handling showMenu action, type:', data.type);
        showOverlay();
        
        if (data.type === 'vehicles') {
            // Redirect to vehicle shop handler
            window.postMessage({
                action: 'showVehicleShop',
                items: data.items,
                money: data.money
            }, '*');
        } else if (data.type === 'classes') {
            // Redirect to class selection handler
            window.postMessage({
                action: 'showClassSelection',
                items: data.items,
                playerLevel: data.playerLevel,
                money: data.money
            }, '*');
        }
    }
});

// Helper function to create vehicle card
function createVehicleCard(vehicle, playerMoney) {
    const card = document.createElement('div');
    card.className = 'vehicle-card';
    
    const canAffordBuy = playerMoney >= vehicle.cost;
    const canAffordRent = playerMoney >= vehicle.rent;
    
    card.innerHTML = `
        <img src="${vehicle.img || 'images/vehicles/default.png'}" alt="${vehicle.name}">
        <h3>${vehicle.name}</h3>
        <div class="vehicle-prices">
            <button class="buy-btn ${!canAffordBuy ? 'disabled' : ''}" 
                    onclick="buyVehicle('${vehicle.name}', ${vehicle.cost})"
                    ${!canAffordBuy ? 'disabled' : ''}>
                Buy: $${vehicle.cost.toLocaleString()}
            </button>
            <button class="rent-btn ${!canAffordRent ? 'disabled' : ''}" 
                    onclick="rentVehicle('${vehicle.name}', ${vehicle.rent})"
                    ${!canAffordRent ? 'disabled' : ''}>
                Rent: $${vehicle.rent.toLocaleString()}
            </button>
        </div>
    `;
    
    return card;
}

// Helper function to create class card
function createClassCard(classItem, playerLevel) {
    const card = document.createElement('div');
    card.className = 'class-card';
    
    const isLocked = classItem.locked || (playerLevel < classItem.requiredLevel);
    
    if (isLocked) {
        card.classList.add('locked');
    }
    
    card.innerHTML = `
        <img src="${classItem.img || 'images/classes/default.png'}" alt="${classItem.name}">
        <h3>${classItem.name}</h3>
        <p>${classItem.unlock}</p>
    `;
    
    card.onclick = function() {
        if (!isLocked) {
            selectClass(classItem.id);
        }
    };
    
    return card;
}

// Helper function to create weapon card
function createWeaponCard(weapon, playerMoney, classId) {
    const card = document.createElement('div');
    card.className = 'weapon-card';
    
    const canAfford = playerMoney >= weapon.price;
    
    if (!canAfford) {
        card.classList.add('disabled');
    }
    
    card.innerHTML = `
        <img src="${weapon.img || 'images/guns/default.png'}" alt="${weapon.name}">
        <h3>${weapon.name}</h3>
        <p class="weapon-price">$${weapon.price.toLocaleString()}</p>
    `;
    
    card.onclick = function() {
        if (canAfford) {
            selectWeapon(weapon.weapon, classId, weapon.price);
        }
    };
    
    return card;
}

// Vehicle purchase functions
window.buyVehicle = function(name, cost) {
    fetch(`https://${GetParentResourceName()}/buyVehicle`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: name, cost: cost })
    });
};

window.rentVehicle = function(name, rent) {
    fetch(`https://${GetParentResourceName()}/rentVehicle`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: name, rent: rent })
    });
};

// Class selection function
window.selectClass = function(classId) {
    fetch(`https://${GetParentResourceName()}/selectClass`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: classId })
    });
};

// Weapon selection function
window.selectWeapon = function(weapon, classId, price) {
    fetch(`https://${GetParentResourceName()}/selectWeapon`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ weapon: weapon, class: classId, price: price })
    });
};

// Ensure close buttons work for all shops
document.addEventListener('DOMContentLoaded', function() {
    // Vehicle shop close button
    const vehicleCloseBtn = document.getElementById('vehicle-shop-close');
    if (vehicleCloseBtn) {
        vehicleCloseBtn.onclick = function() {
            document.getElementById('vehicles-shop').style.display = 'none';
            document.getElementById('overlay').style.display = 'none';
            fetch(`https://${GetParentResourceName()}/closeMenu`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({})
            });
        };
    }
    
    // Weapon shop close button
    const weaponCloseBtn = document.getElementById('shop-close');
    if (weaponCloseBtn) {
        weaponCloseBtn.onclick = function() {
            document.getElementById('weapons-shop').style.display = 'none';
            document.getElementById('overlay').style.display = 'none';
            fetch(`https://${GetParentResourceName()}/closeMenu`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({})
            });
        };
    }
    
    // Class selection close button
    const classCloseBtn = document.getElementById('classes-close');
    if (classCloseBtn) {
        classCloseBtn.onclick = function() {
            document.getElementById('classes-selection').style.display = 'none';
            document.getElementById('overlay').style.display = 'none';
            fetch(`https://${GetParentResourceName()}/closeMenu`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({})
            });
        };
    }
});

console.log('[KOTH SHOP FIX] Shop display fix loaded');
