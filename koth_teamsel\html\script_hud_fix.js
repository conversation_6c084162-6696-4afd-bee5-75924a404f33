// HUD FIX: Ensure player stats are properly displayed

// Override the updatePlayerData function to ensure it works correctly
window.addEventListener('message', function(event) {
    const data = event.data;
    
    if (data.action === 'updatePlayerData' && data.data) {
        console.log('[KOTH HUD FIX] Received player data update:', data.data);
        
        // Update all HUD elements with the player data
        const playerData = data.data;
        
        // Update player name
        const nameElement = document.getElementById('player-name');
        if (nameElement && playerData.player_name) {
            nameElement.textContent = playerData.player_name;
            console.log('[KOTH HUD FIX] Updated player name to:', playerData.player_name);
        }
        
        // Update money display
        const moneyElement = document.getElementById('player-money-display');
        if (moneyElement && typeof playerData.money === 'number') {
            moneyElement.textContent = '$' + playerData.money.toLocaleString();
            console.log('[KOTH HUD FIX] Updated money to:', '$' + playerData.money.toLocaleString());
            
            // Flash effect to show update
            moneyElement.style.color = '#00ff00';
            setTimeout(() => {
                moneyElement.style.color = '#28a745';
            }, 500);
        }
        
        // Update level
        const levelElement = document.getElementById('player-level');
        if (levelElement && typeof playerData.level === 'number') {
            levelElement.textContent = playerData.level;
            console.log('[KOTH HUD FIX] Updated level to:', playerData.level);
        }
        
        // Update XP with proper calculation
        const xpElement = document.getElementById('player-xp');
        if (xpElement && typeof playerData.xp === 'number') {
            // Calculate XP display based on level
            const levels = [
                {level: 1, required: 0},
                {level: 2, required: 100},
                {level: 3, required: 250},
                {level: 4, required: 500},
                {level: 5, required: 1000},
                {level: 6, required: 1750},
                {level: 7, required: 2750},
                {level: 8, required: 4000},
                {level: 9, required: 6000},
                {level: 10, required: 8500}
            ];
            
            let currentLevelXP = 0;
            let nextLevelXP = 100;
            
            for (let i = 0; i < levels.length; i++) {
                if (levels[i].level === playerData.level) {
                    currentLevelXP = levels[i].required;
                    if (i + 1 < levels.length) {
                        nextLevelXP = levels[i + 1].required;
                    } else {
                        nextLevelXP = currentLevelXP + 1000; // Default increment for max level
                    }
                    break;
                }
            }
            
            const progressXP = playerData.xp - currentLevelXP;
            const neededXP = nextLevelXP - currentLevelXP;
            const displayXP = Math.max(0, Math.min(progressXP, neededXP));
            
            xpElement.textContent = displayXP + '/' + neededXP;
            console.log('[KOTH HUD FIX] Updated XP to:', displayXP + '/' + neededXP);
        }
        
        // Store the data globally for other functions
        if (window.currentGameData) {
            window.currentGameData.playerName = playerData.player_name || window.currentGameData.playerName;
            window.currentGameData.playerMoney = playerData.money || window.currentGameData.playerMoney;
            window.currentGameData.playerLevel = playerData.level || window.currentGameData.playerLevel;
            window.currentGameData.playerXP = playerData.xp || window.currentGameData.playerXP;
        }
    }
});

// Function to manually update HUD (can be called from console for testing)
function forceUpdateHUD(playerName, money, level, xp) {
    console.log('[KOTH HUD FIX] Force updating HUD');
    
    const data = {
        player_name: playerName || 'TestPlayer',
        money: money || 1000,
        level: level || 1,
        xp: xp || 0
    };
    
    window.postMessage({
        action: 'updatePlayerData',
        data: data
    }, '*');
}

// Ensure HUD is visible on load
document.addEventListener('DOMContentLoaded', function() {
    console.log('[KOTH HUD FIX] Ensuring HUD visibility');
    
    const gameHud = document.getElementById('game-hud');
    if (gameHud) {
        gameHud.style.display = 'block';
        gameHud.style.visibility = 'visible';
        gameHud.style.opacity = '1';
        console.log('[KOTH HUD FIX] Game HUD made visible');
    }
    
    // Check if elements exist
    console.log('[KOTH HUD FIX] HUD Elements Check:');
    console.log('  player-name:', !!document.getElementById('player-name'));
    console.log('  player-money-display:', !!document.getElementById('player-money-display'));
    console.log('  player-level:', !!document.getElementById('player-level'));
    console.log('  player-xp:', !!document.getElementById('player-xp'));
});

console.log('[KOTH HUD FIX] Script loaded');
