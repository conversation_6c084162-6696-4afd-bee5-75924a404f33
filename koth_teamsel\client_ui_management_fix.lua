-- UI MANAGEMENT FIX: Ensure all UIs are properly hidden after team selection

local activeUI = nil
local uiStates = {
    teamSelect = false,
    vehicleMenu = false,
    classMenu = false,
    weaponShop = false,
    deathScreen = false
}

-- Function to hide all UI elements
local function hideAllUI()
    print('[KOTH UI FIX] Hiding all UI elements')
    
    -- Send hide message for all possible UI elements
    SendNUIMessage({ action = 'hideAll' })
    SendNUIMessage({ action = 'hideTeamSelect' })
    SendNUIMessage({ action = 'hideMenu' })
    SendNUIMessage({ action = 'hideWeaponSelect' })
    SendNUIMessage({ action = 'hideDeathScreen' })
    SendNUIMessage({ action = 'hideVehicleShop' })
    SendNUIMessage({ action = 'hideClassSelection' })
    
    -- Ensure NUI focus is disabled
    SetNuiFocus(false, false)
    
    -- Reset all UI states
    for key, _ in pairs(uiStates) do
        uiStates[key] = false
    end
    
    activeUI = nil
    
    print('[KOTH UI FIX] All UI elements hidden and focus disabled')
end

-- Override spawn event to ensure UI is hidden
RegisterNetEvent('koth:spawnPlayer', function(spawnData)
    print('[KOTH UI FIX] Player spawning, ensuring UI is hidden')
    
    -- Hide all UI elements
    hideAllUI()
    
    -- Small delay to ensure UI is fully hidden
    Citizen.SetTimeout(500, function()
        -- Double-check UI is hidden
        hideAllUI()
        
        -- Ensure player can control their character
        local playerPed = PlayerPedId()
        if DoesEntityExist(playerPed) then
            -- Enable all controls
            for i = 0, 31 do
                EnableControlAction(0, i, true)
            end
            
            -- Ensure player isn't in any menus
            if IsPauseMenuActive() then
                SetPauseMenuActive(false)
            end
        end
        
        print('[KOTH UI FIX] Post-spawn UI cleanup complete')
    end)
end)

-- Monitor for stuck UI and provide escape mechanism
Citizen.CreateThread(function()
    while true do
        -- Check if any UI is supposed to be active
        local shouldHaveUI = false
        for _, state in pairs(uiStates) do
            if state then
                shouldHaveUI = true
                break
            end
        end
        
        -- If no UI should be active but NUI focus is on, disable it
        if not shouldHaveUI and IsNuiFocusKeeping() then
            print('[KOTH UI FIX] Detected stuck NUI focus, clearing...')
            SetNuiFocus(false, false)
            hideAllUI()
        end
        
        Citizen.Wait(1000) -- Check every second
    end
end)

-- Emergency UI reset command
RegisterCommand('fixui', function()
    print('[KOTH UI FIX] Manual UI reset requested')
    hideAllUI()
    
    -- Force close any possible UI elements
    SendNUIMessage({ action = 'forceCloseAll' })
    
    -- Reset NUI focus multiple times to ensure it's cleared
    for i = 1, 3 do
        SetNuiFocus(false, false)
        Citizen.Wait(100)
    end
    
    -- Notify player
    BeginTextCommandThefeedPost("STRING")
    AddTextComponentSubstringPlayerName("UI has been reset")
    EndTextCommandThefeedPostTicker(false, true)
end, false)

-- Monitor NUI callbacks to track UI state
RegisterNUICallback('uiOpened', function(data, cb)
    if data.type then
        print('[KOTH UI FIX] UI opened:', data.type)
        activeUI = data.type
        uiStates[data.type] = true
    end
    cb('ok')
end)

RegisterNUICallback('uiClosed', function(data, cb)
    if data.type then
        print('[KOTH UI FIX] UI closed:', data.type)
        if activeUI == data.type then
            activeUI = nil
        end
        uiStates[data.type] = false
    end
    cb('ok')
end)

-- Ensure UI is hidden when closing menus
RegisterNUICallback('closeMenu', function(data, cb)
    print('[KOTH UI FIX] Menu close requested')
    hideAllUI()
    cb('ok')
end)

-- Override team selection to ensure proper UI cleanup
RegisterNUICallback('selectTeam', function(data, cb)
    print('[KOTH UI FIX] Team selected, preparing UI cleanup')
    
    -- Mark team select as closing
    uiStates.teamSelect = false
    
    -- Schedule UI cleanup after team selection processing
    Citizen.SetTimeout(1000, function()
        hideAllUI()
    end)
    
    cb('ok')
end)

print('[KOTH UI FIX] UI management fix loaded')
