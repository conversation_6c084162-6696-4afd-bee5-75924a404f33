print('[KOTH] Client loading...')

-- TEAM SPAWN COORDS
local teamSpawns = {
  red   = { x=2238.15, y=3788.91, z=35.89, heading=120.5 },
  blue  = { x=1323.77, y=3143.33, z=40.41, heading=282.48 },
  green = { x=1865.99, y=2607.15, z=45.67, heading=276.45 },
}

-- Player variables
local playerTeam = nil
local playerStats = nil
local hasSelectedTeam = false

-- Request team counts and show UI
AddEventHandler('playerSpawned', function()
  Citizen.SetTimeout(2000, function()
    print('[KOTH] Player spawned, checking team status...')
    
    -- Request player data first
    TriggerServerEvent('koth:requestPlayerData')
    
    -- Only show team selection if player hasn't selected a team
    if not hasSelectedTeam then
      print('[KOTH] Requesting team counts...')
      TriggerServerEvent('koth:requestCounts')
    end
  end)
end)

-- Handle team restoration (when player already has a team)
RegisterNetEvent('koth:restoreTeam', function(team)
  print('[KOTH] Restoring player to team:', team)
  playerTeam = team
  hasSelectedTeam = true
  
  -- Initialize HUD
  SendNUIMessage({ 
    action = 'initHUD',
    playerData = playerStats
  })
  
  -- Show game HUD
  SendNUIMessage({ action = 'hideAll' })
end)

-- Handle spawn event from server
RegisterNetEvent('koth:spawnPlayer', function(spawnData)
  print('[KOTH] Spawning player at team location')
  
  local playerPed = PlayerPedId()
  
  -- Set spawn position
  SetEntityCoords(playerPed, spawnData.x, spawnData.y, spawnData.z, false, false, false, true)
  SetEntityHeading(playerPed, spawnData.heading or 0.0)
  
  -- Ensure player is on ground
  SetEntityCollision(playerPed, true, true)
  FreezeEntityPosition(playerPed, false)
  
  -- Give basic weapon
  GiveWeaponToPed(playerPed, GetHashKey('WEAPON_PISTOL'), 250, false, true)
  
  -- Initialize HUD
  SendNUIMessage({ 
    action = 'initHUD',
    playerData = playerStats
  })
  
  -- Enable PVP
  SetCanAttackFriendly(playerPed, true, false)
  NetworkSetFriendlyFireOption(true)
  
  print('[KOTH] Player spawned successfully')
end)

-- Receive counts and show team select
RegisterNetEvent('koth:updateCounts', function(counts)
  print('[KOTH] Received counts, showing UI')
  if counts then
    SendNUIMessage({ action='showTeamSelect', counts=counts })
    SetNuiFocus(true, true)
  end
end)

-- Team selection callback
RegisterNUICallback('selectTeam', function(data, cb)
  print('[KOTH] Team selected:', data.team or 'none')
  if data and data.team then
    playerTeam = data.team
    hasSelectedTeam = true
    TriggerServerEvent('koth:pickTeam', data.team)
    SendNUIMessage({ action='hideAll' })
    SetNuiFocus(false, false)
  end
  cb('ok')
end)

-- Vehicle menu - triggered by ped interaction
RegisterNetEvent('koth:openVehicleMenu', function(items)
  print('[KOTH] Opening vehicle menu')

  -- Request fresh player data when opening shop (but don't wait)
  if not playerStats or not playerStats.money then
    print('[KOTH] Player stats not loaded, requesting data...')
    TriggerServerEvent('koth:requestPlayerData')
    -- Use default values for now
    playerStats = playerStats or { money = 0, level = 1, xp = 0 }
  end

  -- Debug player stats
  print(('[KOTH] Player stats available - Money: $%d, Level: %d'):format(playerStats.money or 0, playerStats.level or 0))

  -- Use provided items or comprehensive vehicle data
  local vehicles = items or {
    { name = 'Blista', cost = 8000, rent = 1200, img = 'images/vehicles/blista.png' },
    { name = 'Futo', cost = 12000, rent = 1800, img = 'images/vehicles/futo.png' },
    { name = 'Sultan', cost = 15000, rent = 2200, img = 'images/vehicles/sultan.png' },
    { name = 'Elegy', cost = 18000, rent = 2700, img = 'images/vehicles/elegy.png' },
    { name = 'Kuruma', cost = 22000, rent = 3300, img = 'images/vehicles/kuruma.png' },
    { name = 'Armored Kuruma', cost = 35000, rent = 5200, img = 'images/vehicles/kuruma_armored.png' },
    { name = 'Insurgent', cost = 45000, rent = 6700, img = 'images/vehicles/insurgent.png' },
    { name = 'Technical', cost = 28000, rent = 4200, img = 'images/vehicles/technical.png' },
    { name = 'Sandking XL', cost = 32000, rent = 4800, img = 'images/vehicles/sandking.png' },
    { name = 'Mesa', cost = 25000, rent = 3700, img = 'images/vehicles/mesa.png' },
    { name = 'Buzzard', cost = 85000, rent = 12700, img = 'images/vehicles/buzzard.png' },
    { name = 'Savage', cost = 120000, rent = 18000, img = 'images/vehicles/savage.png' },
    { name = 'Rhino Tank', cost = 150000, rent = 22500, img = 'images/vehicles/rhino.png' },
    { name = 'Hydra', cost = 200000, rent = 30000, img = 'images/vehicles/hydra.png' }
  }

  -- Always request fresh player data before opening vehicle shop
  TriggerServerEvent('koth:requestPlayerData')

  -- Wait a moment for data to arrive, then open shop
  Citizen.SetTimeout(200, function()
    local playerMoney = playerStats and playerStats.money or 0
    print(('[KOTH] Opening vehicle shop - Player money: $%d'):format(playerMoney))
    print(('[KOTH] PlayerStats exists: %s'):format(playerStats and 'YES' or 'NO'))
    if playerStats then
      print(('[KOTH] PlayerStats content: money=%d, level=%d, xp=%d'):format(playerStats.money or 0, playerStats.level or 0, playerStats.xp or 0))
    end

    SendNUIMessage({
      action='showMenu',
      type='vehicles',
      items=vehicles,
      money = playerMoney
    })
  end)
  SetNuiFocus(true, true)
end)

-- Class menu - triggered by ped interaction
RegisterNetEvent('koth:openClassMenu', function(items)
  print('[KOTH] Opening class menu')

  -- Request fresh player data when opening shop (but don't wait)
  if not playerStats or not playerStats.money then
    print('[KOTH] Player stats not loaded, requesting data...')
    TriggerServerEvent('koth:requestPlayerData')
    -- Use default values for now
    playerStats = playerStats or { money = 0, level = 1, xp = 0 }
  end

  local playerLevel = playerStats and playerStats.level or 1
  print(('[KOTH] Player level: %d'):format(playerLevel))

  -- Use provided items or exact classes from the image
  local classes = items or {
    {
      id = 'assault',
      name = 'Assault',
      unlock = 'Unlocked',
      img = 'images/classes/assault.png',
      requiredLevel = 1
    },
    {
      id = 'medic',
      name = 'Medic',
      unlock = 'Unlock at level 5',
      img = 'images/classes/medic.png',
      requiredLevel = 5
    },
    {
      id = 'engineer',
      name = 'Engineer',
      unlock = 'Unlock at level 15',
      img = 'images/classes/engineer.png',
      requiredLevel = 15
    },
    {
      id = 'heavy',
      name = 'Heavy',
      unlock = 'Unlock at level 25',
      img = 'images/classes/heavy.png',
      requiredLevel = 25
    },
    {
      id = 'scout',
      name = 'Scout',
      unlock = 'Unlock at level 40',
      img = 'images/classes/scout.png',
      requiredLevel = 40
    }
  }

  -- Add locked status based on player level
  for i, class in ipairs(classes) do
    class.locked = playerLevel < class.requiredLevel
    if class.locked then
      print(('[KOTH] Class %s is locked (requires level %d, player is %d)'):format(class.name, class.requiredLevel, playerLevel))
    end
  end

  local playerMoney = playerStats and playerStats.money or 0
  print(('[KOTH] Sending class menu - Level: %d, Money: $%d'):format(playerLevel, playerMoney))

  SendNUIMessage({
    action='showMenu',
    type='classes',
    items=classes,
    playerLevel = playerLevel,
    money = playerMoney
  })
  SetNuiFocus(true, true)
end)

-- Vehicle purchase callbacks
RegisterNUICallback('buyVehicle', function(data, cb)
  print('[KOTH] Buying vehicle:', data.name or 'none')
  if data and data.name then
    TriggerServerEvent('koth:buyVehicle', data.name)
  end
  SendNUIMessage({ action='hideAll' })
  SetNuiFocus(false, false)
  cb('ok')
end)

RegisterNUICallback('rentVehicle', function(data, cb)
  print('[KOTH] Renting vehicle:', data.name or 'none')
  if data and data.name then
    TriggerServerEvent('koth:rentVehicle', data.name)
  end
  SendNUIMessage({ action='hideAll' })
  SetNuiFocus(false, false)
  cb('ok')
end)

-- Class selection callback - now shows weapon selection
RegisterNUICallback('selectClass', function(data, cb)
  print('[KOTH] Class selected:', data.id or 'none')
  if data and data.id then
    -- Check if player has required level for this class
    local playerLevel = playerStats and playerStats.level or 1
    local requiredLevels = {
      assault = 1,
      medic = 5,
      engineer = 15,
      heavy = 25,
      scout = 40
    }

    local requiredLevel = requiredLevels[data.id] or 1
    if playerLevel < requiredLevel then
      -- Show error message
      BeginTextCommandThefeedPost("STRING")
      AddTextComponentSubstringPlayerName(('Class locked! Requires level %d (you are level %d)'):format(requiredLevel, playerLevel))
      EndTextCommandThefeedPostTicker(false, true)
      cb('ok')
      return
    end
    -- Define weapons exactly as shown in the image with prices
    local allWeapons = {
      { weapon = 'WEAPON_STONE_HATCHET', name = 'Stone Hatchet', price = 150, img = 'images/guns/stone_hatchet.png' },
      { weapon = 'WEAPON_CROWBAR', name = 'Crowbar', price = 200, img = 'images/guns/crowbar.png' },
      { weapon = 'WEAPON_HATCHET', name = 'Hatchet', price = 250, img = 'images/guns/hatchet.png' },
      { weapon = 'WEAPON_PISTOL', name = 'Pistol', price = 500, img = 'images/guns/pistol.png' },
      { weapon = 'WEAPON_MICROSMG', name = 'Micro SMG', price = 800, img = 'images/guns/micro_smg.png' },
      { weapon = 'WEAPON_COMBATPDW', name = 'Combat PDW', price = 1200, img = 'images/guns/combat_pdw.png' },
      { weapon = 'WEAPON_ASSAULTSMG', name = 'Assault SMG', price = 1500, img = 'images/guns/assault_smg.png' },
      { weapon = 'WEAPON_BULLPUPRIFLE', name = 'Bullpup Rifle', price = 2500, img = 'images/guns/bullpup_rifle.png' },
      { weapon = 'WEAPON_HEAVYRIFLE', name = 'Heavy Rifle', price = 3000, img = 'images/guns/heavy_rifle.png' },
      { weapon = 'WEAPON_ASSAULTRIFLE', name = 'Assault Rifle', price = 3500, img = 'images/guns/assault_rifle.png' },
      { weapon = 'WEAPON_SPECIALCARBINE', name = 'Special Carbine', price = 4000, img = 'images/guns/special_carbine.png' },
      { weapon = 'WEAPON_SPECIALCARBINE_MK2', name = 'Special Carbine Mk2', price = 5000, img = 'images/guns/special_carbine_mk2.png' },
      { weapon = 'WEAPON_CARBINERIFLE_MK2', name = 'Carbine Rifle Mk2', price = 5500, img = 'images/guns/carbine_rifle_mk2.png' }
    }

    -- Request fresh money data directly from server for weapon shop
    print('[KOTH] Requesting fresh money data for weapon shop...')
    TriggerServerEvent('koth:getMoneyForWeaponShop', data.id, allWeapons)

    -- Reset flag and set fallback timeout to prevent UI getting stuck
    weaponShopOpened = false
    Citizen.SetTimeout(3000, function()
      if not weaponShopOpened then
        print('[KOTH] Weapon shop timeout - opening with default money')
        SendNUIMessage({
          action = 'showWeaponSelect',
          class = data.id,
          weapons = allWeapons,
          money = playerStats and playerStats.money or 0
        })
      end
    end)
  end
  cb('ok')
end)

-- Weapon selection callback
RegisterNUICallback('selectWeapon', function(data, cb)
  print('[KOTH] Weapon selected:', data.weapon or 'none', 'for class:', data.class or 'none', 'price:', data.price or 'none')
  if data and data.weapon and data.class then
    TriggerServerEvent('koth:selectLoadout', data.class, data.weapon, data.price)
  end
  SendNUIMessage({ action='hideAll' })
  SetNuiFocus(false, false)
  cb('ok')
end)

-- Receive weapon from server and give to player
RegisterNetEvent('koth:giveWeapon', function(weapon, classId, price)
  print(('[KOTH] Receiving weapon: %s for class: %s (price: $%s)'):format(weapon or 'none', classId or 'none', price or 'free'))

  if weapon then
    local playerPed = PlayerPedId()

    -- Remove all weapons first
    RemoveAllPedWeapons(playerPed, true)

    -- Give the selected weapon with ammo
    GiveWeaponToPed(playerPed, GetHashKey(weapon), 250, false, true)

    -- Set as current weapon
    SetCurrentPedWeapon(playerPed, GetHashKey(weapon), true)

    print(('[KOTH] Successfully equipped %s'):format(weapon))

    -- Show notification with price
    BeginTextCommandThefeedPost("STRING")
    local weaponName = weapon:gsub('WEAPON_', ''):gsub('_', ' ')
    local message = ('Purchased %s for $%s'):format(weaponName, price or '0')
    AddTextComponentSubstringPlayerName(message)
    EndTextCommandThefeedPostTicker(false, true)
  end
end)

-- Purchase result handler
RegisterNetEvent('koth:purchaseResult', function(success, message)
  print(('[KOTH] Purchase result: %s - %s'):format(success and 'SUCCESS' or 'FAILED', message))

  -- Show notification
  BeginTextCommandThefeedPost("STRING")
  AddTextComponentSubstringPlayerName(message)
  EndTextCommandThefeedPostTicker(false, true)

  -- If purchase failed, keep the shop open
  if not success then
    -- You could add specific UI feedback here
    print('[KOTH] Purchase failed, keeping shop open')
  end
end)

-- Close menu callback
RegisterNUICallback('closeMenu', function(data, cb)
  print('[KOTH] Menu closed')
  SendNUIMessage({ action='hideAll' })
  SetNuiFocus(false, false)
  cb('ok')
end)

-- Vehicle spawning handler
RegisterNetEvent('koth:spawnVehicle', function(vehicleName, purchaseType, price)
  print(('[KOTH] Spawning vehicle: %s (%s for $%s)'):format(vehicleName or 'none', purchaseType or 'unknown', price or '0'))

  if vehicleName then
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local playerHeading = GetEntityHeading(playerPed)

    -- Vehicle name mapping (simplified)
    local vehicleModels = {
      ['Blista'] = 'blista',
      ['Futo'] = 'futo',
      ['Sultan'] = 'sultan',
      ['Elegy'] = 'elegy2',
      ['Kuruma'] = 'kuruma',
      ['Armored Kuruma'] = 'kuruma2',
      ['Insurgent'] = 'insurgent',
      ['Technical'] = 'technical',
      ['Sandking XL'] = 'sandking',
      ['Mesa'] = 'mesa',
      ['Buzzard'] = 'buzzard2',
      ['Savage'] = 'savage',
      ['Rhino Tank'] = 'rhino',
      ['Hydra'] = 'hydra'
    }

    local modelName = vehicleModels[vehicleName] or vehicleName:lower()
    local modelHash = GetHashKey(modelName)

    -- Request model
    RequestModel(modelHash)
    local timeout = GetGameTimer() + 5000
    while not HasModelLoaded(modelHash) and GetGameTimer() < timeout do
      Citizen.Wait(1)
    end

    if HasModelLoaded(modelHash) then
      -- Find spawn position (in front of player)
      local spawnX = playerCoords.x + math.cos(math.rad(playerHeading)) * 5.0
      local spawnY = playerCoords.y + math.sin(math.rad(playerHeading)) * 5.0

      -- Get proper ground Z coordinate to prevent falling through map
      local foundGround, groundZ = GetGroundZFor_3dCoord(spawnX, spawnY, playerCoords.z + 50.0, false)
      local spawnZ = foundGround and (groundZ + 1.0) or (playerCoords.z + 1.0)

      -- Create vehicle
      local vehicle = CreateVehicle(modelHash, spawnX, spawnY, spawnZ, playerHeading, true, false)

      if DoesEntityExist(vehicle) then
        -- Ensure vehicle is properly placed on ground
        SetEntityCoords(vehicle, spawnX, spawnY, spawnZ, false, false, false, true)
        SetVehicleOnGroundProperly(vehicle)

        -- Set player as owner
        SetVehicleHasBeenOwnedByPlayer(vehicle, true)
        SetEntityAsMissionEntity(vehicle, true, true)

        -- Ensure vehicle has proper collision with ground
        SetEntityCollision(vehicle, true, true)

        -- Wait a moment for physics to settle
        Citizen.Wait(100)

        -- Put player in vehicle
        TaskWarpPedIntoVehicle(playerPed, vehicle, -1)

        print(('[KOTH] Successfully spawned %s at ground level'):format(vehicleName))

        -- Show notification
        BeginTextCommandThefeedPost("STRING")
        local message = ('%s %s for $%s'):format(purchaseType == 'rent' and 'Rented' or 'Purchased', vehicleName, price or '0')
        AddTextComponentSubstringPlayerName(message)
        EndTextCommandThefeedPostTicker(false, true)
      else
        print(('[KOTH] Failed to create vehicle: %s'):format(vehicleName))
      end

      SetModelAsNoLongerNeeded(modelHash)
    else
      print(('[KOTH] Failed to load model: %s'):format(modelName))
    end
  end
end)

-- PED SPAWNING SYSTEM
local spawnedPeds = {}

-- Ped configurations for each team
local pedSpawns = {
  red = {
    { model = `s_m_y_marine_01`,   offset = vector3( 3.0,  2.0, 0.0), text = 'Click [E] To Open Classes Menu',  event = 'koth:openClassMenu'  },
    { model = `s_m_m_autoshop_02`, offset = vector3(-3.0, -2.0, 0.0), text = 'Click [E] To Open Vehicle Menu', event = 'koth:openVehicleMenu' },
  },
  blue = {
    { model = `s_m_y_marine_01`,   offset = vector3( 3.0,  2.0, 0.0), text = 'Click [E] To Open Classes Menu',  event = 'koth:openClassMenu'  },
    { model = `s_m_m_autoshop_02`, offset = vector3(-3.0, -2.0, 0.0), text = 'Click [E] To Open Vehicle Menu', event = 'koth:openVehicleMenu' },
  },
  green = {
    { model = `s_m_y_marine_01`,   offset = vector3( 3.0,  2.0, 0.0), text = 'Click [E] To Open Classes Menu',  event = 'koth:openClassMenu'  },
    { model = `s_m_m_autoshop_02`, offset = vector3(-3.0, -2.0, 0.0), text = 'Click [E] To Open Vehicle Menu', event = 'koth:openVehicleMenu' },
  },
}

-- 3D text helper function
local function Draw3DText(x, y, z, text)
  local onScreen, _x, _y = World3dToScreen2d(x, y, z + 1.0)
  if onScreen then
    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextCentre(true)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    AddTextComponentString(text)
    DrawText(_x, _y)
    local factor = (#text) / 370
    DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 0, 0, 0, 120)
  end
end

-- Spawn peds when resource starts
AddEventHandler('onClientResourceStart', function(res)
  if GetCurrentResourceName() ~= res then return end

  Citizen.CreateThread(function()
    print('[KOTH] Starting ped spawning...')
    Citizen.Wait(3000) -- Wait for everything to load

    for team, list in pairs(pedSpawns) do
      local sv = teamSpawns[team]
      if not sv then
        print('[KOTH] WARNING: No spawn data for team ' .. team)
      else
        local basePos = vector3(sv.x, sv.y, sv.z)
        print(('[KOTH] Spawning peds for team %s at %.2f, %.2f, %.2f'):format(team, basePos.x, basePos.y, basePos.z))

        for _, info in ipairs(list) do
          -- Validate model
          if not (IsModelValid(info.model) and IsModelInCdimage(info.model)) then
            print(('[KOTH] SKIP: Invalid model %s for team %s'):format(info.model, team))
          else
            -- Load model
            RequestModel(info.model)
            local timeout = GetGameTimer() + 5000
            while not HasModelLoaded(info.model) and GetGameTimer() < timeout do
              Citizen.Wait(1)
            end

            if not HasModelLoaded(info.model) then
              print(('[KOTH] FAIL: Could not load model %s'):format(info.model))
            else
              -- Find ground level
              local spawnX, spawnY = basePos.x + info.offset.x, basePos.y + info.offset.y
              local foundGround, groundZ = GetGroundZFor_3dCoord(spawnX, spawnY, basePos.z + 50.0, false)
              local finalZ = foundGround and groundZ or basePos.z

              -- Spawn ped
              local ped = CreatePed(4, info.model, spawnX, spawnY, finalZ, sv.heading or 0.0, false, true)

              if DoesEntityExist(ped) then
                SetEntityHeading(ped, sv.heading or 0.0)
                FreezeEntityPosition(ped, true)
                SetEntityInvincible(ped, true)
                SetBlockingOfNonTemporaryEvents(ped, true)

                table.insert(spawnedPeds, {
                  ped = ped,
                  text = info.text,
                  event = info.event
                })

                print(('[KOTH] SUCCESS: Spawned %s ped for team %s'):format(info.model, team))
              else
                print(('[KOTH] FAIL: CreatePed failed for %s'):format(info.model))
              end

              SetModelAsNoLongerNeeded(info.model)
            end
          end
        end
      end
    end

    print(('[KOTH] Ped spawning complete. Total peds: %d'):format(#spawnedPeds))
  end)
end)

-- Interaction loop for peds
Citizen.CreateThread(function()
  while true do
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)

    for _, info in ipairs(spawnedPeds) do
      if DoesEntityExist(info.ped) then
        local pedCoords = GetEntityCoords(info.ped)
        local distance = #(playerCoords - pedCoords)

        if distance < 10.0 then
          Draw3DText(pedCoords.x, pedCoords.y, pedCoords.z, info.text)

          if distance < 1.5 and IsControlJustReleased(0, 38) then -- E key
            print(('[KOTH] Player interacted with ped: %s'):format(info.event))
            TriggerEvent(info.event)
          end
        end
      end
    end

    Citizen.Wait(0)
  end
end)

-- SAFE ZONE SYSTEM
local inSafeZone = false
local safeZoneRadius = 25.0 -- Radius of safe zones
local teamBlips = {} -- Store map blips

-- KOTH ZONE SYSTEM
local kothZone = {
  x = 2842.4216,
  y = 2864.8088,
  z = 62.5975,
  radius = 150.0, -- Massive zone radius
  controllingTeam = nil, -- 'red', 'blue', 'green', or nil for neutral
  captureProgress = 0.0, -- 0.0 to 100.0
  captureRate = 1.0, -- Points per second when capturing
  playersInZone = {}, -- Track players in zone by team
  blip = nil, -- Map blip for the zone
  centerBlip = nil -- Center marker blip
}

local inKothZone = false
local kothZoneColors = {
  neutral = { r = 128, g = 128, b = 128, a = 50 }, -- Grey
  red = { r = 255, g = 0, b = 0, a = 50 },
  blue = { r = 0, g = 100, b = 255, a = 50 },
  green = { r = 0, g = 255, b = 0, a = 50 }
}

local kothBlipColors = {
  neutral = 8, -- Grey
  red = 1,     -- Red
  blue = 3,    -- Blue
  green = 2    -- Green
}

-- Zone colors for each team
local zoneColors = {
  red = { r = 255, g = 0, b = 0, a = 50 },
  blue = { r = 0, g = 100, b = 255, a = 50 },
  green = { r = 0, g = 255, b = 0, a = 50 }
}

-- Map blip colors (GTA blip color IDs)
local blipColors = {
  red = 1,    -- Red
  blue = 3,   -- Blue
  green = 2   -- Green
}

-- Create map blips for team zones
local function createTeamBlips()
  print('[KOTH] Creating team zone blips...')

  for team, spawn in pairs(teamSpawns) do
    local blip = AddBlipForRadius(spawn.x, spawn.y, spawn.z, safeZoneRadius)

    -- Set blip properties
    SetBlipColour(blip, blipColors[team] or 0)
    SetBlipAlpha(blip, 128) -- Semi-transparent

    -- Create center blip for the team
    local centerBlip = AddBlipForCoord(spawn.x, spawn.y, spawn.z)
    SetBlipSprite(centerBlip, 84) -- Shield icon
    SetBlipColour(centerBlip, blipColors[team] or 0)
    SetBlipScale(centerBlip, 1.0)
    SetBlipAsShortRange(centerBlip, true)

    -- Set blip name
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentSubstringPlayerName(team:upper() .. " Team Safe Zone")
    EndTextCommandSetBlipName(centerBlip)

    -- Store blips for cleanup
    teamBlips[team] = {
      radius = blip,
      center = centerBlip
    }

    print(('[KOTH] Created %s team blip at %.2f, %.2f'):format(team, spawn.x, spawn.y))
  end
  
  -- Create KOTH zone blip
  createKothBlip()
end

-- Initialize blips when
