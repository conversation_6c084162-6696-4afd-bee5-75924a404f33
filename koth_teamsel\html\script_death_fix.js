// DEATH SCREEN FIX: Ensure death screen displays properly and updates correctly

// Handle death screen messages
window.addEventListener('message', function(event) {
    const data = event.data;
    
    if (data.action === 'showDeathScreen') {
        console.log('[KOTH DEATH FIX] Showing death screen for killer:', data.killer);
        
        // Show death screen
        const deathScreen = document.getElementById('death-screen');
        if (deathScreen) {
            deathScreen.style.display = 'flex';
            deathScreen.style.visibility = 'visible';
            deathScreen.style.opacity = '1';
            
            // Add fade-in animation
            deathScreen.style.animation = 'fadeIn 0.5s ease-in';
        }
        
        // Update killer info
        const killerNameElement = document.getElementById('killer-name');
        const killerIdElement = document.getElementById('killer-id');
        
        if (killerNameElement) {
            killerNameElement.textContent = data.killer || 'Unknown';
        }
        
        if (killerIdElement) {
            killerIdElement.textContent = data.killerId || '0';
        }
        
        // Reset respawn progress
        const respawnFill = document.getElementById('respawn-fill');
        if (respawnFill) {
            respawnFill.style.width = '0%';
        }
        
        // Start bleedout timer at 50 seconds
        const bleedoutTimer = document.getElementById('bleedout-timer');
        if (bleedoutTimer) {
            bleedoutTimer.textContent = '50';
        }
    }
    
    else if (data.action === 'hideDeathScreen') {
        console.log('[KOTH DEATH FIX] Hiding death screen');
        
        const deathScreen = document.getElementById('death-screen');
        if (deathScreen) {
            // Add fade-out animation
            deathScreen.style.animation = 'fadeOut 0.5s ease-out';
            
            setTimeout(() => {
                deathScreen.style.display = 'none';
                deathScreen.style.visibility = 'hidden';
                deathScreen.style.opacity = '0';
            }, 500);
        }
    }
    
    else if (data.action === 'updateDeathTimer') {
        // Update bleedout timer
        const bleedoutTimer = document.getElementById('bleedout-timer');
        if (bleedoutTimer) {
            bleedoutTimer.textContent = data.bleedoutTimer || '0';
            
            // Add urgency color as timer gets low
            if (data.bleedoutTimer <= 10) {
                bleedoutTimer.style.color = '#ff0000';
                bleedoutTimer.style.animation = 'pulse 1s infinite';
            } else if (data.bleedoutTimer <= 20) {
                bleedoutTimer.style.color = '#ff6600';
            } else {
                bleedoutTimer.style.color = '#ffffff';
                bleedoutTimer.style.animation = 'none';
            }
        }
    }
    
    else if (data.action === 'updateRespawnProgress') {
        // Update respawn progress bar
        const respawnFill = document.getElementById('respawn-fill');
        if (respawnFill) {
            respawnFill.style.width = data.progress + '%';
            
            // Change color as it fills
            if (data.progress >= 75) {
                respawnFill.style.backgroundColor = '#00ff00';
            } else if (data.progress >= 50) {
                respawnFill.style.backgroundColor = '#ffff00';
            } else {
                respawnFill.style.backgroundColor = '#ff0000';
            }
        }
    }
});

// Add CSS animations if not already present
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    #death-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }
    
    .death-content {
        text-align: center;
        color: white;
    }
    
    .death-title {
        font-size: 48px;
        font-weight: bold;
        color: #ff0000;
        margin-bottom: 30px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    }
    
    .respawn-section {
        margin: 20px 0;
    }
    
    .respawn-instruction {
        font-size: 24px;
        margin-bottom: 10px;
    }
    
    .key-highlight {
        color: #00ff00;
        font-weight: bold;
        padding: 2px 8px;
        border: 2px solid #00ff00;
        border-radius: 4px;
    }
    
    .respawn-bar {
        width: 300px;
        height: 20px;
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid white;
        border-radius: 10px;
        margin: 0 auto;
        overflow: hidden;
    }
    
    .respawn-fill {
        height: 100%;
        background: #ff0000;
        transition: width 0.1s ease-out;
    }
    
    .bleedout-section {
        margin: 30px 0;
    }
    
    .bleedout-text {
        font-size: 20px;
        color: #ff6600;
    }
    
    #bleedout-timer {
        font-weight: bold;
        font-size: 24px;
    }
    
    .medic-call {
        font-size: 18px;
        margin-top: 10px;
        color: #ffff00;
    }
    
    .medic-highlight {
        color: #00ff00;
        font-weight: bold;
    }
    
    .killer-info {
        margin-top: 40px;
        font-size: 18px;
    }
    
    .killer-id {
        color: #ff6600;
    }
    
    .killer-name {
        color: #ff0000;
        font-weight: bold;
    }
`;

document.head.appendChild(style);

console.log('[KOTH DEATH FIX] Death screen script loaded');
