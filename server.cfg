endpoint_add_tcp "*************:30166" 
endpoint_add_udp "*************:30166" 
set sv_listingIPOverride "*************" 

# This file is a minimal version of the default config file.
# This is NOT supposed to be enough for most servers.
# Please read the fivem documentation:
#   https://aka.cfx.re/server-commands
#   https://docs.fivem.net/docs/server-manual/setting-up-a-server/

## You SHOULD edit the following:
sv_hostname "Vantage KOTH built with FiveM Basic Server!"
sets sv_projectName "[FiveM Basic Server] Vantage KOTH"
sets sv_projectDesc "Recipe for the base resources required to run a minimal FiveM server."
sets tags "default, deployer, koth, pvp"
sets locale "root-AQ"

## You CAN edit the following:
sv_enforceGameBuild 3258 #mp2024_01	- Bottom Dollar Bounties
sv_licenseKey "rtybojvj8d79f3ocdwawczmmjeul90n4"
sv_maxclients 10
set steam_webApiKey "none"
set resources_useSystemChat true

## FIX FOR GAME CRASHES (OneSync Population Issue)
set onesync_enableLegacy true
set onesync true

## DATABASE CONFIGURATION (MySQL for KOTH Money/XP System)
set mysql_connection_string "mysql://zap1190649-1:<EMAIL>/zap1190649-1?charset=utf8mb4"
## These resources will start by default.
ensure mapmanager
ensure chat
ensure spawnmanager
ensure sessionmanager
ensure basic-gamemode
ensure hardcap

## Database Resource (Required for KOTH Money/XP System)
ensure oxmysql

## Custom Resources
ensure Pingu_manor

## KOTH Gamemode (Money & XP System Enabled)
ensure koth_teamsel

## Admin Menu
exec @vMenu/config/permissions.cfg
ensure vMenu

## Add system admins
add_ace group.admin command allow # allow all commands
add_ace group.admin command.quit deny # but don't allow quit
# Deployer Note: this admin master has no identifiers to be automatically added.
# add_principal identifier.discord:111111111111111111 group.admin #example
