# KOTH Team Selection System - Testing & Troubleshooting Guide

## Quick Fix Applied
I've added UI visibility fixes to ensure the team selection screen appears. The fixes include:

1. **client_ui_fix.lua** - Forces the UI to display with multiple attempts
2. **html/script_ui_fix.js** - Ensures overlay and team select elements are visible
3. Updated **fxmanifest.lua** to include the fix files
4. Updated **html/ui.html** to load both scripts

## Testing Steps

### 1. Initial Testing
After restarting the resource, test these commands:

```
/showteamselect  - Manually shows team selection UI
/testui         - Tests the UI display with force show
```

### 2. Check Console Output
Press F8 to open the console and look for these messages:
- `[KOTH] Client loading...`
- `[KOTH] Requesting team counts...`
- `[KOTH] Received counts, showing UI`
- `[KOTH] NUI message sent and focus set`

### 3. Browser Console
Press F12 in-game to check browser console for:
- `[KOTH] Script loading...`
- `[KOTH] Received message: showTeamSelect`
- `[KOTH UI FIX] Ensuring team select visibility`

## Common Issues & Solutions

### Issue 1: UI Not Showing
**Symptoms:** Player is frozen but no UI appears

**Solutions:**
1. Use `/showteamselect` command
2. Check F8 console for errors
3. Ensure resource is started: `ensure koth_teamsel`
4. Restart resource: `restart koth_teamsel`

### Issue 2: Database Connection Failed
**Symptoms:** Player data not loading, $0 money shown

**Solutions:**
1. Verify MySQL credentials in server.lua:
   ```lua
   host = "mysql-mariadb-oce02-11-101.zap-srv.com",
   port = 3306,
   database = "zap1190649-1",
   username = "zap1190649-1",
   password = "tYXDWyEmvqykO75w"
   ```
2. Ensure oxmysql is started before koth_teamsel
3. Check server console for database errors

### Issue 3: Peds Not Spawning
**Symptoms:** No shop peds at team spawns

**Solutions:**
1. Wait 3 seconds after resource start
2. Check F8 console for ped spawn messages
3. Teleport to spawn coords to verify:
   - Red: `/tp 2238.15 3788.91 35.89`
   - Blue: `/tp 1323.77 3143.33 40.41`
   - Green: `/tp 1865.99 2607.15 45.67`

### Issue 4: Shops Not Opening
**Symptoms:** Pressing E near peds does nothing

**Solutions:**
1. Ensure you're within 1.5 units of the ped
2. Check if 3D text appears above ped
3. Verify playerStats is loaded (check F8 console)

## Full System Test Checklist

### ✅ Team Selection
- [ ] UI appears on spawn
- [ ] Can select a team
- [ ] Player spawns at correct location
- [ ] Player is unfrozen after selection

### ✅ Database Integration
- [ ] Player stats load from database
- [ ] Money displays correctly in HUD
- [ ] Level and XP show properly
- [ ] Stats persist across sessions

### ✅ Safe Zones
- [ ] PVP disabled in spawn areas
- [ ] Safe zone markers visible
- [ ] PVP re-enables when leaving

### ✅ Shop Systems
- [ ] Vehicle shop opens with E key
- [ ] Weapon shop opens after class selection
- [ ] Prices display correctly
- [ ] Can't buy items without enough money
- [ ] Purchases deduct money properly

### ✅ KOTH Zone
- [ ] Zone marker visible at quarry
- [ ] Capture progress works
- [ ] Points awarded to controlling team
- [ ] Zone status updates in HUD

### ✅ Combat System
- [ ] Can damage other players outside safe zones
- [ ] Kill rewards money and XP
- [ ] Death screen appears
- [ ] Can respawn with E key hold
- [ ] Respawn at team base

### ✅ HUD Elements
- [ ] Team player counts update
- [ ] Zone points display
- [ ] Health bar works
- [ ] Money updates on purchases/kills
- [ ] Level up notifications appear

## Debug Commands

Add these to server console for testing:

```lua
-- Give player money
/run exports.koth_teamsel:GivePlayerMoney(1, 10000)

-- Give player XP
/run exports.koth_teamsel:GivePlayerXP(1, 500)

-- Set player level
/run exports.koth_teamsel:SetPlayerLevel(1, 10)

-- Check player data
/checkstats
/checkdata
```

## Performance Monitoring

Monitor these in F8 console:
- Resource memory usage: `resmon`
- Check for script errors
- Watch for database query delays

## Next Steps if Issues Persist

1. **Clear Cache**
   - Delete FiveM cache folder
   - Restart FiveM client

2. **Check Dependencies**
   - Ensure oxmysql is latest version
   - Verify all files are uploaded correctly

3. **Database Reset**
   - Run database.sql again to ensure tables exist
   - Check phpMyAdmin for data

4. **Contact Support**
   - Provide F8 console logs
   - Share browser console errors (F12)
   - Include server console output

## Resource Load Order

Ensure this order in server.cfg:
```
ensure oxmysql
ensure koth_teamsel
ensure koth_admin
```

## Testing Sequence

1. Start server
2. Join game
3. Wait for UI (use `/showteamselect` if needed)
4. Select team
5. Test shop interactions
6. Enter KOTH zone
7. Test combat
8. Check money/XP updates

This guide should help you thoroughly test all systems and troubleshoot any issues!
