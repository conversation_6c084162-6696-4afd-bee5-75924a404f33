// JavaScript for new death screen UI

window.addEventListener('message', function(event) {
    const data = event.data;
    const deathScreen = document.getElementById('death-screen');
    const respawnFill = document.getElementById('respawn-fill');
    const killerName = document.getElementById('killer-name');
    const bleedoutTimer = document.getElementById('bleedout-timer');

    if (data.action === 'showDeathScreen') {
        if (deathScreen) {
            deathScreen.style.display = 'block';
        }
        if (killerName) {
            killerName.textContent = data.killer || 'Unknown';
        }
        if (respawnFill) {
            respawnFill.style.width = '0%';
        }
        if (bleedoutTimer) {
            bleedoutTimer.textContent = '50';
        }
    } else if (data.action === 'hideDeathScreen') {
        if (deathScreen) {
            deathScreen.style.display = 'none';
        }
    } else if (data.action === 'updateRespawnProgress') {
        if (respawnFill) {
            respawnFill.style.width = data.progress + '%';
        }
    } else if (data.action === 'updateDeathTimer') {
        if (bleedoutTimer) {
            bleedoutTimer.textContent = data.bleedoutTimer || '0';
        }
    }
});

console.log('[KOTH DEATH NEW] Death screen script loaded');
