// UI FIX: Add handler for forceShowOverlay
window.addEventListener('message', function(ev) {
  if (ev.data && ev.data.action === 'forceShowOverlay') {
    console.log('[KOTH UI FIX] Force showing overlay');
    const overlay = document.getElementById('overlay');
    if (overlay) {
      overlay.style.display = 'flex';
      overlay.style.visibility = 'visible';
      overlay.style.opacity = '1';
    }
  }
});

// Also ensure team select is visible when showTeamSelect is called
const originalHandler = window.onmessage;
window.addEventListener('message', function(ev) {
  if (ev.data && ev.data.action === 'showTeamSelect') {
    console.log('[KOTH UI FIX] Ensuring team select visibility');
    
    // Force overlay to be visible
    const overlay = document.getElementById('overlay');
    if (overlay) {
      overlay.style.display = 'flex';
      overlay.style.visibility = 'visible';
      overlay.style.opacity = '1';
    }
    
    // Force team select to be visible
    const teamSelect = document.getElementById('team-select');
    if (teamSelect) {
      teamSelect.style.display = 'flex';
      teamSelect.style.visibility = 'visible';
      teamSelect.style.opacity = '1';
    }
  }
});

// Debug function to check element visibility
function debugUIVisibility() {
  const overlay = document.getElementById('overlay');
  const teamSelect = document.getElementById('team-select');
  
  console.log('[KOTH UI DEBUG] Overlay:', {
    exists: !!overlay,
    display: overlay ? overlay.style.display : 'N/A',
    computedDisplay: overlay ? window.getComputedStyle(overlay).display : 'N/A'
  });
  
  console.log('[KOTH UI DEBUG] Team Select:', {
    exists: !!teamSelect,
    display: teamSelect ? teamSelect.style.display : 'N/A',
    computedDisplay: teamSelect ? window.getComputedStyle(teamSelect).display : 'N/A'
  });
}

// Run debug on load
document.addEventListener('DOMContentLoaded', function() {
  console.log('[KOTH UI FIX] DOM loaded, running visibility debug');
  debugUIVisibility();
});
