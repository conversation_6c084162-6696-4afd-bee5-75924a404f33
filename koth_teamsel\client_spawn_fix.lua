-- SPAWN FIX: Override default spawn behavior to prevent team selection on death

local hasSelectedTeam = false
local playerTeam = nil

-- Track when player selects a team
RegisterNetEvent('koth:teamSelected', function(team)
    hasSelectedTeam = true
    playerTeam = team
    print('[KOTH SPAWN FIX] Team selected:', team)
end)

-- Override the playerSpawned event to prevent team selection UI
AddEventHandler('playerSpawned', function()
    print('[KOTH SPAWN FIX] playerSpawned event - hasSelectedTeam:', hasSelectedTeam)

    -- Check if death system has team info
    local deathSystemTeam = nil
    if exports['koth_teamsel'] and exports['koth_teamsel'].getPlayerTeam then
        deathSystemTeam = exports['koth_teamsel']:getPlayerTeam()
    end

    -- If player has already selected a team (either here or in death system), don't show team selection
    if hasSelectedTeam or deathSystemTeam then
        print('[KOTH SPAWN FIX] Blocking team selection UI - player already has a team')

        -- Sync team info if needed
        if deathSystemTeam and not hasSelectedTeam then
            hasSelectedTeam = true
            playerTeam = deathSystemTeam
        end

        -- Hide any UI that might be showing
        SendNUIMessage({ action = 'hideAll' })
        SetNuiFocus(false, false)

        -- Make sure player isn't frozen (unless they're dead)
        local playerPed = PlayerPedId()
        if not IsEntityDead(playerPed) then
            FreezeEntityPosition(playerPed, false)
            SetEntityInvincible(playerPed, false)
        end

        -- Don't trigger team selection
        return
    end

    -- Only show team selection for first spawn
    print('[KOTH SPAWN FIX] First spawn - allowing team selection')
end)

-- Track team from spawn event
RegisterNetEvent('koth:spawnPlayer', function(spawnData)
    -- Determine team from spawn location
    local teamSpawns = {
        red = { x = 2238.15, y = 3788.91 },
        blue = { x = 1323.77, y = 3143.33 },
        green = { x = 1865.99, y = 2607.15 }
    }
    
    for team, spawn in pairs(teamSpawns) do
        if math.abs(spawnData.x - spawn.x) < 1.0 and math.abs(spawnData.y - spawn.y) < 1.0 then
            playerTeam = team
            hasSelectedTeam = true
            print('[KOTH SPAWN FIX] Detected team from spawn:', team)
            
            -- Notify death system
            if exports['koth_teamsel'] and exports['koth_teamsel'].setPlayerTeam then
                exports['koth_teamsel']:setPlayerTeam(team)
            end
            break
        end
    end
end)

-- Override team selection callback to track selection
RegisterNUICallback('selectTeam', function(data, cb)
    if data and data.team then
        playerTeam = data.team
        hasSelectedTeam = true
        print('[KOTH SPAWN FIX] Team selected via UI:', data.team)

        -- Notify death system
        if exports['koth_teamsel'] and exports['koth_teamsel'].setPlayerTeam then
            exports['koth_teamsel']:setPlayerTeam(data.team)
        end

        -- Trigger server event to spawn player
        TriggerServerEvent('koth:pickTeam', data.team)
    end
    cb('ok')
end)

-- Disable auto-spawn to prevent conflicts
Citizen.CreateThread(function()
    exports.spawnmanager:setAutoSpawn(false)
    exports.spawnmanager:forceRespawn()
end)

print('[KOTH SPAWN FIX] Spawn fix loaded')
