<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Death Screen Rework</title>
<style>
  body, html {
    margin: 0; padding: 0; width: 100%; height: 100%;
    background: rgba(0,0,0,0.85);
    display: flex; justify-content: center; align-items: center;
    font-family: Arial, sans-serif;
    color: white;
  }
  #death-screen {
    text-align: center;
    max-width: 400px;
    width: 90%;
  }
  #death-title {
    font-size: 48px;
    color: #ff4444;
    margin-bottom: 20px;
  }
  #killer-info {
    margin-bottom: 20px;
    font-size: 20px;
  }
  #respawn-instruction {
    font-size: 18px;
    margin-bottom: 10px;
  }
  #respawn-bar {
    width: 100%;
    height: 25px;
    background: #333;
    border-radius: 12px;
    overflow: hidden;
  }
  #respawn-fill {
    height: 100%;
    width: 0%;
    background: #44ff44;
    transition: width 0.1s linear;
  }
  #bleedout-timer {
    margin-top: 20px;
    font-size: 16px;
    color: #ffcc00;
  }
</style>
</head>
<body>
  <div id="death-screen" style="display:none;">
    <div id="death-title">YOU DIED</div>
    <div id="killer-info">Killed by: <span id="killer-name">Unknown</span></div>
    <div id="respawn-instruction">Hold <strong>E</strong> for 5 seconds to respawn</div>
    <div id="respawn-bar"><div id="respawn-fill"></div></div>
    <div id="bleedout-timer">Auto respawn in 50 seconds</div>
  </div>

<script src="death_rework.js"></script>
</body>
</html>
